using System;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using IPIS.Utils;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Repositories.Interfaces;
using System.Media;
using System.IO;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using IPIS.Models;
using System.Threading.Tasks;

namespace IPIS.Forms.Announcement
{
    public partial class AnnouncementBoardForm : Form
    {
        private DataGridView trainGrid;
        private Panel rightSidebar;
        private NumericUpDown repeatCountUpDown;
        private Button stopButton, playButton, pauseButton;
        private TrackBar volumeTrackBar;
        private CheckedListBox slogansListBox;
        private TextBox trainNoTextBox;
        private Button loadButton, clearButton, refreshButton;
        private Label statusLabel;
        private readonly TrainService trainService;
        private readonly AdvertisingService advertisingService;
        private readonly StationService stationService;
        private readonly LanguageService languageService;
        private readonly StationLanguageService stationLanguageService;
        private readonly AnnouncementTemplateService announcementTemplateService;
        private readonly SQLiteAnnouncementSequenceRepository sequenceRepository;
        private readonly SQLiteSequenceItemRepository sequenceItemRepository;
        private readonly ToastNotification toast;
        private bool isEditMode = false;
        private bool isDataBinding = false;

        // Audio playback related fields - Using AudioPlayer utility for background playback
        private AudioPlayer audioPlayer;
        private string fullAudioPath;

        // Refresh timer and configuration
        private System.Windows.Forms.Timer refreshTimer;
        private int refreshIntervalMinutes = 2; // Default 2 minutes
        private bool autoRefreshEnabled = true;
        private bool loadTrainsForCurrentDayAndTime = true; // Default behavior

        // Auto load/delete timers and configuration
        private System.Windows.Forms.Timer autoLoadTimer;
        private System.Windows.Forms.Timer autoDeleteTimer;
        private StationDetails currentStation;

        public AnnouncementBoardForm()
        {
            InitializeComponent();
            trainService = new TrainService(new SQLiteTrainRepository());
            advertisingService = new AdvertisingService(new SQLiteAdvertisingRepository());
            stationService = new StationService(new SQLiteStationRepository());
            languageService = new LanguageService(new SQLiteLanguageRepository());
            stationLanguageService = new StationLanguageService(
                new SQLiteStationLanguageConfigRepository(),
                new SQLiteStationAnnouncementConfigRepository(),
                new SQLiteLanguageRepository());
            announcementTemplateService = new AnnouncementTemplateService(new SQLiteAnnouncementTemplateRepository());
            sequenceRepository = new SQLiteAnnouncementSequenceRepository();
            sequenceItemRepository = new SQLiteSequenceItemRepository();
            toast = new ToastNotification(this);

            // Initialize audio playback components - using AudioPlayer utility for background playback
            audioPlayer = new AudioPlayer();
            audioPlayer.SetStatusCallback(UpdateStatus);
            audioPlayer.PlaybackCompleted += (s, e) => UpdateStatus("Audio playback completed");

            // Initialize full audio path - using the same approach as Sequence Management
            fullAudioPath = Path.Combine(Application.StartupPath, "data", "WAVE");
            LogDebug($"Audio path initialized: {fullAudioPath}");

            // Verify audio directory exists
            if (!Directory.Exists(fullAudioPath))
            {
                LogDebug($"Warning: Audio directory not found at {fullAudioPath}", true);
                // Try alternative paths
                string[] possiblePaths = new[]
                {
                    Path.Combine(Application.StartupPath, "data", "WAVE"),
                    Path.Combine(Application.StartupPath, "..", "data", "WAVE"),
                    Path.Combine(Application.StartupPath, "..", "..", "data", "WAVE"),
                    Path.Combine(Application.StartupPath, "..", "..", "..", "data", "WAVE")
                };

                foreach (string path in possiblePaths)
                {
                    if (Directory.Exists(path))
                    {
                        fullAudioPath = path;
                        LogDebug($"Found audio directory at: {fullAudioPath}");
                        break;
                    }
                }
            }

            // Load current station configuration
            LoadCurrentStationConfiguration();

            // Initialize refresh timer
            InitializeRefreshTimer();

            // Initialize auto load/delete timers
            InitializeAutoLoadDeleteTimers();

            // Update sidebar labels with current station configuration
            UpdateSidebarLabels();

            // Update platform dropdowns with current station platforms
            UpdatePlatformDropdowns();

            LoadTrainData();
            LoadAdvertisingData();
        }

        private void LoadCurrentStationConfiguration()
        {
            try
            {
                currentStation = stationService.GetCurrentStation();
                if (currentStation != null)
                {
                    // Update refresh interval from current station configuration
                    refreshIntervalMinutes = currentStation.AutoLoadInterval;
                    autoRefreshEnabled = currentStation.AutoLoad;

                    LogDebug($"Loaded current station configuration: {currentStation.StationName} (Code: {currentStation.StationCode})");
                    LogDebug($"Auto Load: {currentStation.AutoLoad}, Interval: {currentStation.AutoLoadInterval} minutes");
                    LogDebug($"Auto Delete: {currentStation.AutoDelete}, Interval: {currentStation.AutoDeleteInterval} minutes");
                    LogDebug($"Available Platforms: {currentStation.AvailablePF}");
                }
                else
                {
                    LogDebug("No current station found, using default configuration");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error loading current station configuration: {ex.Message}");
            }
        }

        private void InitializeAutoLoadDeleteTimers()
        {
            // Auto Load Timer
            autoLoadTimer = new System.Windows.Forms.Timer();
            autoLoadTimer.Tick += AutoLoadTimer_Tick;

            // Auto Delete Timer
            autoDeleteTimer = new System.Windows.Forms.Timer();
            autoDeleteTimer.Tick += AutoDeleteTimer_Tick;

            // Start timers if enabled in current station configuration
            if (currentStation != null)
            {
                if (currentStation.AutoLoad)
                {
                    autoLoadTimer.Interval = currentStation.AutoLoadInterval * 60 * 1000; // Convert minutes to milliseconds
                    autoLoadTimer.Start();
                    LogDebug($"Auto load timer started with {currentStation.AutoLoadInterval} minute interval");
                }

                if (currentStation.AutoDelete)
                {
                    autoDeleteTimer.Interval = currentStation.AutoDeleteInterval * 60 * 1000; // Convert minutes to milliseconds
                    autoDeleteTimer.Start();
                    LogDebug($"Auto delete timer started with {currentStation.AutoDeleteInterval} minute interval");
                }
            }
        }

        private void AutoLoadTimer_Tick(object sender, EventArgs e)
        {
            LogDebug("Auto load timer triggered");
            try
            {
                LoadTrainData();
                UpdateStatus("Auto load completed");
            }
            catch (Exception ex)
            {
                LogDebug($"Error in auto load: {ex.Message}");
            }
        }

        private void AutoDeleteTimer_Tick(object sender, EventArgs e)
        {
            LogDebug("Auto delete timer triggered");
            try
            {
                // Remove trains that have passed their scheduled time by more than the post interval
                var onlineTrains = trainService.GetOnlineTrains();
                if (onlineTrains != null)
                {
                    DateTime now = DateTime.Now;
                    int postIntervalMinutes = currentStation?.AutoDeletePostInterval ?? 30;

                    foreach (DataRow row in onlineTrains.Rows)
                    {
                        string trainAD = row["Train_AD"]?.ToString();
                        string schAT = row["Sch_AT"]?.ToString();
                        string schDT = row["Sch_DT"]?.ToString();

                        if (!string.IsNullOrEmpty(trainAD) && !string.IsNullOrEmpty(schAT) && !string.IsNullOrEmpty(schDT))
                        {
                            DateTime scheduledTime;
                            if (trainAD == "A" && TimeSpan.TryParse(schAT, out TimeSpan arrTime))
                            {
                                scheduledTime = DateTime.Today.Add(arrTime);
                            }
                            else if (trainAD == "D" && TimeSpan.TryParse(schDT, out TimeSpan depTime))
                            {
                                scheduledTime = DateTime.Today.Add(depTime);
                            }
                            else
                            {
                                continue;
                            }

                            // If train has passed its scheduled time by more than post interval, remove it
                            if (now > scheduledTime.AddMinutes(postIntervalMinutes))
                            {
                                string trainNo = row["Train_No"]?.ToString();
                                if (!string.IsNullOrEmpty(trainNo))
                                {
                                    trainService.DeleteOnlineTrain(trainNo);
                                    LogDebug($"Auto deleted train {trainNo} (scheduled: {scheduledTime:HH:mm}, current: {now:HH:mm})");
                                }
                            }
                        }
                    }

                    // Refresh grid if any trains were deleted
                    RefreshGridFromDatabase();
                }
                UpdateStatus("Auto delete completed");
            }
            catch (Exception ex)
            {
                LogDebug($"Error in auto delete: {ex.Message}");
            }
        }

        private List<string> GetCurrentStationPlatforms()
        {
            var platforms = new List<string>();

            if (currentStation != null)
            {
                LogDebug($"Getting platforms for station: {currentStation.StationName}, AvailablePF: {currentStation.AvailablePF}");

                // Add platforms based on AvailablePF count
                for (int i = 1; i <= currentStation.AvailablePF && i <= 10; i++)
                {
                    string platformValue = "";
                    switch (i)
                    {
                        case 1: platformValue = currentStation.P1; break;
                        case 2: platformValue = currentStation.P2; break;
                        case 3: platformValue = currentStation.P3; break;
                        case 4: platformValue = currentStation.P4; break;
                        case 5: platformValue = currentStation.P5; break;
                        case 6: platformValue = currentStation.P6; break;
                        case 7: platformValue = currentStation.P7; break;
                        case 8: platformValue = currentStation.P8; break;
                        case 9: platformValue = currentStation.P9; break;
                        case 10: platformValue = currentStation.P10; break;
                    }

                    if (!string.IsNullOrEmpty(platformValue))
                    {
                        platforms.Add(platformValue);
                        LogDebug($"Added platform {i}: {platformValue}");
                    }
                    else
                    {
                        platforms.Add(i.ToString()); // Fallback to number
                        LogDebug($"Added platform {i}: {i} (fallback)");
                    }
                }

                LogDebug($"Total platforms added: {platforms.Count}");
            }
            else
            {
                LogDebug("No current station found, using default platforms");
                // Fallback to default platforms if no current station
                for (int i = 1; i <= 10; i++)
                {
                    platforms.Add(i.ToString());
                }
            }

            return platforms;
        }

        private string GetTrainNameWithStationCode(string trainNo)
        {
            try
            {
                var trainData = trainService.GetTrainByNumber(trainNo);
                if (trainData != null)
                {
                    string trainName = trainData["Train_NameEng"]?.ToString() ?? "";
                    string srcStation = trainData["Src_Stn"]?.ToString() ?? "";
                    string destStation = trainData["Desti_Stn"]?.ToString() ?? "";

                    // Use source station code as prefix
                    if (!string.IsNullOrEmpty(srcStation) && !string.IsNullOrEmpty(trainName))
                    {
                        return $"{srcStation} - {trainName}";
                    }
                    else if (!string.IsNullOrEmpty(trainName))
                    {
                        return trainName;
                    }
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error getting train name with station code: {ex.Message}");
            }

            return trainNo; // Fallback to train number
        }

        public async Task RefreshCurrentStationConfiguration()
        {
            try
            {
                LoadCurrentStationConfiguration();

                // Restart timers with new configuration
                if (autoLoadTimer != null)
                {
                    autoLoadTimer.Stop();
                    if (currentStation?.AutoLoad == true)
                    {
                        autoLoadTimer.Interval = currentStation.AutoLoadInterval * 60 * 1000;
                        autoLoadTimer.Start();
                    }
                }

                if (autoDeleteTimer != null)
                {
                    autoDeleteTimer.Stop();
                    if (currentStation?.AutoDelete == true)
                    {
                        autoDeleteTimer.Interval = currentStation.AutoDeleteInterval * 60 * 1000;
                        autoDeleteTimer.Start();
                    }
                }

                // Update refresh timer
                if (refreshTimer != null)
                {
                    refreshTimer.Stop();
                    refreshIntervalMinutes = currentStation?.AutoLoadInterval ?? 2;
                    autoRefreshEnabled = currentStation?.AutoLoad ?? false;

                    if (autoRefreshEnabled)
                    {
                        refreshTimer.Interval = refreshIntervalMinutes * 60 * 1000;
                        refreshTimer.Start();
                    }
                }

                // Update sidebar labels
                UpdateSidebarLabels();

                // Update platform dropdowns
                UpdatePlatformDropdowns();

                // Log current language preferences
                await LogCurrentLanguagePreferences();

                LogDebug("Current station configuration refreshed");
                UpdateStatus("Current station configuration refreshed");
            }
            catch (Exception ex)
            {
                LogDebug($"Error refreshing current station configuration: {ex.Message}");
            }
        }

        private void UpdateSidebarLabels()
        {
            try
            {
                // Find and update the station info label
                foreach (Control control in rightSidebar.Controls)
                {
                    if (control is Label label)
                    {
                        if (label.Text.Contains("No current station") || label.Text.Contains("(") && label.Text.Contains(")"))
                        {
                            // This is likely the station info label
                            label.Text = currentStation != null ? $"{currentStation.StationName} ({currentStation.StationCode})" : "No current station";
                            label.ForeColor = currentStation != null ? Color.Lime : Color.Orange;
                            break;
                        }
                    }
                }

                // Find and update auto load status label
                foreach (Control control in rightSidebar.Controls)
                {
                    if (control is Label label && label.Text.StartsWith("Auto Load:"))
                    {
                        label.Text = currentStation != null ? $"Auto Load: {(currentStation.AutoLoad ? "Enabled" : "Disabled")} ({currentStation.AutoLoadInterval} mins)" : "Auto Load: Not configured";
                        label.ForeColor = currentStation?.AutoLoad == true ? Color.Lime : Color.Orange;
                        break;
                    }
                }

                // Find and update auto delete status label
                foreach (Control control in rightSidebar.Controls)
                {
                    if (control is Label label && label.Text.StartsWith("Auto Delete:"))
                    {
                        label.Text = currentStation != null ? $"Auto Delete: {(currentStation.AutoDelete ? "Enabled" : "Disabled")} ({currentStation.AutoDeleteInterval} mins)" : "Auto Delete: Not configured";
                        label.ForeColor = currentStation?.AutoDelete == true ? Color.Lime : Color.Orange;
                        break;
                    }
                }

                // Update refresh interval numeric up down
                foreach (Control control in rightSidebar.Controls)
                {
                    if (control is NumericUpDown upDown && upDown.Minimum == 1 && upDown.Maximum == 60)
                    {
                        upDown.Value = refreshIntervalMinutes;
                        break;
                    }
                }

                // Update auto refresh checkbox
                foreach (Control control in rightSidebar.Controls)
                {
                    if (control is CheckBox checkBox && checkBox.Text == "Enable Auto Refresh")
                    {
                        checkBox.Checked = autoRefreshEnabled;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error updating sidebar labels: {ex.Message}");
            }
        }

        private void UpdatePlatformDropdowns()
        {
            try
            {
                var platforms = GetCurrentStationPlatforms();
                LogDebug($"Updating platform dropdowns with {platforms.Count} platforms: {string.Join(", ", platforms)}");

                // Update PF column
                if (trainGrid.Columns["PF"] is DataGridViewComboBoxColumn pfCol)
                {
                    pfCol.Items.Clear();
                    if (platforms.Count > 0)
                    {
                        pfCol.Items.AddRange(platforms.ToArray());
                        LogDebug($"Updated PF column with {platforms.Count} platforms");
                    }
                    else
                    {
                        // Fallback to default platforms if no current station
                        pfCol.Items.AddRange(new object[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "10" });
                        LogDebug("Updated PF column with default platforms");
                    }
                }

                // Update Changed PF column
                if (trainGrid.Columns["ChangedPF"] is DataGridViewComboBoxColumn changedPFCol)
                {
                    changedPFCol.Items.Clear();
                    if (platforms.Count > 0)
                    {
                        changedPFCol.Items.AddRange(platforms.ToArray());
                        LogDebug($"Updated ChangedPF column with {platforms.Count} platforms");
                    }
                    else
                    {
                        // Fallback to default platforms if no current station
                        changedPFCol.Items.AddRange(new object[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "10" });
                        LogDebug("Updated ChangedPF column with default platforms");
                    }
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error updating platform dropdowns: {ex.Message}");
            }
        }



        private void InitializeRefreshTimer()
        {
            refreshTimer = new System.Windows.Forms.Timer();
            refreshTimer.Interval = refreshIntervalMinutes * 60 * 1000; // Convert minutes to milliseconds
            refreshTimer.Tick += RefreshTimer_Tick;

            if (autoRefreshEnabled)
            {
                refreshTimer.Start();
                LogDebug($"Auto-refresh timer started with {refreshIntervalMinutes} minute interval from current station configuration");
            }
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            LogDebug("Auto-refresh timer triggered");
            PerformIncrementalRefresh();
            LoadAdvertisingData();
            // Removed status update for auto-refresh as requested
        }

        private async void PerformIncrementalRefresh()
        {
            try
            {
                LogDebug("PerformIncrementalRefresh: Starting incremental refresh");

                // Get current eligible trains from master data
                DataTable currentEligibleTrains = GetCurrentEligibleTrains();
                LogDebug($"PerformIncrementalRefresh: Found {currentEligibleTrains?.Rows.Count ?? 0} currently eligible trains");

                // Get current online trains
                var currentOnlineTrains = trainService.GetOnlineTrains();
                LogDebug($"PerformIncrementalRefresh: Current online trains: {currentOnlineTrains?.Rows.Count ?? 0}");

                if (currentEligibleTrains == null || currentEligibleTrains.Rows.Count == 0)
                {
                    // No eligible trains, clear all online trains
                    if (currentOnlineTrains != null && currentOnlineTrains.Rows.Count > 0)
                    {
                        LogDebug("PerformIncrementalRefresh: No eligible trains found, clearing all online trains");
                        trainService.ClearAllOnlineTrains();
                        RefreshGridFromDatabase();
                    }
                    return;
                }

                // Create sets for efficient comparison
                var eligibleTrainNumbers = new HashSet<string>();
                var onlineTrainNumbers = new HashSet<string>();

                // Build eligible train numbers set
                foreach (DataRow row in currentEligibleTrains.Rows)
                {
                    eligibleTrainNumbers.Add(row["Train_No"].ToString());
                }

                // Build online train numbers set
                if (currentOnlineTrains != null)
                {
                    foreach (DataRow row in currentOnlineTrains.Rows)
                    {
                        onlineTrainNumbers.Add(row["Train_No"].ToString());
                    }
                }

                // Find trains to add (in eligible but not in online)
                var trainsToAdd = eligibleTrainNumbers.Except(onlineTrainNumbers).ToList();

                // Find trains to remove (in online but not in eligible)
                var trainsToRemove = onlineTrainNumbers.Except(eligibleTrainNumbers).ToList();

                LogDebug($"PerformIncrementalRefresh: Trains to add: {trainsToAdd.Count}, Trains to remove: {trainsToRemove.Count}");

                // Remove expired trains in one go
                if (trainsToRemove.Count > 0)
                {
                    LogDebug($"PerformIncrementalRefresh: Removing {trainsToRemove.Count} expired trains");
                    foreach (string trainNo in trainsToRemove)
                    {
                        trainService.DeleteOnlineTrain(trainNo);
                    }
                }

                // Add new eligible trains in one go
                if (trainsToAdd.Count > 0)
                {
                    LogDebug($"PerformIncrementalRefresh: Adding {trainsToAdd.Count} new eligible trains");
                    await AddNewTrainsToOnlineList(trainsToAdd, currentEligibleTrains);
                }

                // Refresh grid only if there were changes
                if (trainsToAdd.Count > 0 || trainsToRemove.Count > 0)
                {
                    RefreshGridFromDatabase();
                    LogDebug($"PerformIncrementalRefresh: Grid refreshed after changes (Added: {trainsToAdd.Count}, Removed: {trainsToRemove.Count})");
                }
                else
                {
                    LogDebug("PerformIncrementalRefresh: No changes detected, grid not refreshed");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error in PerformIncrementalRefresh: {ex.Message}");
                LogDebug($"Stack trace: {ex.StackTrace}");
                // Don't show error toast for auto-refresh to avoid spam
            }
        }

        private DataTable GetCurrentEligibleTrains()
        {
            try
            {
                DataTable filteredTrains = null;

                if (loadTrainsForCurrentDayAndTime)
                {
                    // Load trains for current day and time window (30 minutes ago to end of day)
                    filteredTrains = trainService.GetTrainsForCurrentDayAndTime(120);
                }
                else
                {
                    // Load all trains and filter for current day
                    var allTrains = trainService.GetAllTrains();
                    filteredTrains = FilterTrainsForCurrentDay(allTrains);
                }

                return filteredTrains;
            }
            catch (Exception ex)
            {
                LogDebug($"Error getting current eligible trains: {ex.Message}");
                return null;
            }
        }

        private async Task AddNewTrainsToOnlineList(List<string> trainNumbersToAdd, DataTable eligibleTrains)
        {
            try
            {
                LogDebug($"AddNewTrainsToOnlineList: Adding {trainNumbersToAdd.Count} new trains to online list");

                int addedCount = 0;

                foreach (string trainNo in trainNumbersToAdd)
                {
                    // Find the train data in eligible trains
                    var trainRow = eligibleTrains.Select($"Train_No = '{trainNo}'").FirstOrDefault();
                    if (trainRow != null)
                    {
                        // Add train to online list with default values and station code prefix
                        string trainNameWithPrefix = GetTrainNameWithStationCode(trainNo);
                        trainService.AddOnlineTrain(
                            trainNo,
                            trainNameWithPrefix,
                            trainRow["Train_AD"].ToString(),
                            "RUNNING RIGHT TIME", // Default status
                            "00:00", // Default late time
                            trainRow["Sch_AT"].ToString(), // Expected arrival time
                            trainRow["Sch_DT"].ToString(), // Expected departure time
                            trainRow["Sch_PF"].ToString(), // Platform
                            "False" // Default announcement flag
                        );

                        LogDebug($"Added train {trainNo} to online list");
                        addedCount++;
                    }
                    else
                    {
                        LogDebug($"Train {trainNo} not found in eligible trains data");
                    }
                }

                LogDebug($"AddNewTrainsToOnlineList: Finished adding {addedCount} trains to online list");
            }
            catch (Exception ex)
            {
                LogDebug($"Error adding new trains to online list: {ex.Message}");
                throw;
            }
        }

        private void RefreshGridFromDatabase()
        {
            try
            {
                LogDebug("RefreshGridFromDatabase: Refreshing grid from database");

                isDataBinding = true;
                trainGrid.CellValueChanged -= TrainGrid_CellValueChanged;
                trainGrid.CellClick -= TrainGrid_CellClick;

                // Get updated data from database
                var onlineTrains = trainService.GetOnlineTrains();

                if (onlineTrains != null && onlineTrains.Rows.Count > 0)
                {
                    trainGrid.DataSource = onlineTrains;
                    LogDebug($"RefreshGridFromDatabase: Grid refreshed with {onlineTrains.Rows.Count} trains");

                    EnsureComboBoxValuesPresent("Status");
                    EnsureComboBoxValuesPresent("AD");
                }
                else
                {
                    trainGrid.DataSource = null;
                    trainGrid.Rows.Clear();
                    LogDebug("RefreshGridFromDatabase: Grid cleared - no trains in database");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error refreshing grid from database: {ex.Message}");
            }
            finally
            {
                trainGrid.CellValueChanged += TrainGrid_CellValueChanged;
                trainGrid.CellClick += TrainGrid_CellClick;
                isDataBinding = false;
            }
        }

        private string GetCurrentTimeWindowInfo()
        {
            DateTime now = DateTime.Now;
            TimeSpan currentTime = now.TimeOfDay;
            TimeSpan windowStart = currentTime.Add(TimeSpan.FromMinutes(-30)); // 30 minutes ago
            TimeSpan windowEnd = TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59)); // End of day

            return $"Current time: {currentTime:hh\\:mm}, Window: {windowStart:hh\\:mm} - {windowEnd:hh\\:mm} (future trains)";
        }



        private void InitializeComponent()
        {
            this.Text = "Announcement Board";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            InitializeGrid();
            InitializeSidebar();
            InitializeEventHandlers();
        }

        private void InitializeGrid()
        {
            trainGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                RowHeadersVisible = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false
            };

            // Add tooltip to inform users about right-click functionality
            var toolTip = new ToolTip();
            toolTip.SetToolTip(trainGrid, "Right-click on Late, EAT, or EDT columns to manually update them in the database");

            // Add columns
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "S.No.", Name = "SNo", Width = 60, ReadOnly = true, DataPropertyName = "Sl_No" });
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Train Number", Name = "TrainNumber", Width = 70, DataPropertyName = "Train_No" });
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Train Name", Name = "TrainName", Width = 250, DataPropertyName = "Train_NameEng" });

            var adCol = new DataGridViewComboBoxColumn { HeaderText = "A/D", Name = "AD", Width = 60, DataPropertyName = "Train_AD" };
            adCol.Items.AddRange(new object[] { "A", "D" });
            trainGrid.Columns.Add(adCol);

            // Increased width for Status dropdown to match sequence management
            var statusCol = new DataGridViewComboBoxColumn { HeaderText = "Status", Name = "Status", Width = 250, DataPropertyName = "Train_Status" };
            // Status items will be loaded dynamically from announcement templates
            statusCol.Items.AddRange(new object[] {
                "IS ARRIVING ON",
                "HAS ARRIVED ON",
                "WILL ARRIVE SHORTLY",
                "RUNNING RIGHT TIME",
                "RUNNING LATE",
                "INDEFINITE LATE",
                "CANCELLED",
                "PLATFORM CHANGED",
                "DIVERTED",
                "TERMINATED",
                "DEPARTING", // <-- Make sure this is included!
                "WILL DEPART SHORTLY",
                "HAS DEPARTED",
                "DEPARTURE DELAYED"
            });
            trainGrid.Columns.Add(statusCol);

            // SAT and SDT columns are read-only (non-editable)
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "SAT", Name = "SAT", Width = 55, DataPropertyName = "Sch_AT", ReadOnly = true });
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "SDT", Name = "SDT", Width = 55, DataPropertyName = "Sch_DT", ReadOnly = true });

            // Late, EAT, and EDT columns are excluded from auto-updates but remain editable
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Late", Name = "Late", Width = 55, DataPropertyName = "Late" });
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "EAT", Name = "EAT", Width = 55, DataPropertyName = "Exp_AT" });
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "EDT", Name = "EDT", Width = 55, DataPropertyName = "Exp_DT" });
            // Platform column as dropdown - will be populated after current station is loaded
            var pfCol = new DataGridViewComboBoxColumn { HeaderText = "PF", Name = "PF", Width = 60, DataPropertyName = "Sch_PF" };
            trainGrid.Columns.Add(pfCol);

            // Add new columns for platform change and diversion
            // Changed PF column as dropdown - will be populated after current station is loaded
            var changedPFCol = new DataGridViewComboBoxColumn { HeaderText = "Changed PF", Name = "ChangedPF", Width = 120, DataPropertyName = "Changed_PF", ReadOnly = true };
            trainGrid.Columns.Add(changedPFCol);
            trainGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Diverted From", Name = "DivertedFrom", Width = 250, DataPropertyName = "Diverted_From", ReadOnly = true });

            var anCol = new DataGridViewCheckBoxColumn { HeaderText = "AN", Name = "AN", Width = 50, DataPropertyName = "AN" };
            trainGrid.Columns.Add(anCol);

            var delCol = new DataGridViewButtonColumn
            {
                HeaderText = "Delete",
                Name = "DEL",
                Width = 50,
                Text = "🗑",
                UseColumnTextForButtonValue = true,
                DataPropertyName = "Del_Train",
                FlatStyle = FlatStyle.Flat
            };
            trainGrid.Columns.Add(delCol);

            // Set the last column to fill remaining space
            trainGrid.Columns[trainGrid.Columns.Count - 1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;

            this.Controls.Add(trainGrid);
        }

        private void InitializeSidebar()
        {
            rightSidebar = new Panel
            {
                Dock = DockStyle.Right,
                Width = 400,
                Height = 700,
                BackColor = Color.FromArgb(30, 30, 30),
                AutoScroll = true
            };

            int currentY = 20;
            int sidebarPadding = 20;
            int controlWidth = rightSidebar.Width - 2 * sidebarPadding - 4;
            int buttonSpacing = 8;

            var repeatLabel = new Label { Text = "Repeat Count", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth };
            currentY += repeatLabel.Height + 10;

            repeatCountUpDown = new NumericUpDown { Location = new Point(sidebarPadding, currentY), Width = controlWidth, Minimum = 1, Maximum = 10, Value = 1, Font = new Font("Segoe UI", 11) };
            currentY += repeatCountUpDown.Height + 10;

            // Stop, Play, Pause in a single row
            int tripleButtonWidth = (controlWidth - 2 * buttonSpacing) / 3;
            stopButton = new Button { Text = "Stop", Location = new Point(sidebarPadding, currentY), Width = tripleButtonWidth, Height = 40 };
            playButton = new Button { Text = "Play", Location = new Point(sidebarPadding + tripleButtonWidth + buttonSpacing, currentY), Width = tripleButtonWidth, Height = 40 };
            pauseButton = new Button { Text = "Pause", Location = new Point(sidebarPadding + 2 * (tripleButtonWidth + buttonSpacing), currentY), Width = tripleButtonWidth, Height = 40 };
            ButtonStyler.ApplyStandardStyle(stopButton, "danger");
            ButtonStyler.ApplyStandardStyle(playButton, "success");
            ButtonStyler.ApplyStandardStyle(pauseButton, "warning");
            currentY += stopButton.Height + 10;

            var volumeLabel = new Label { Text = "Volume", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth };
            currentY += volumeLabel.Height + 10;

            volumeTrackBar = new TrackBar { Location = new Point(sidebarPadding, currentY), Width = controlWidth, Minimum = 0, Maximum = 100, Value = 50 };
            currentY += volumeTrackBar.Height + 10;

            var slogansLabel = new Label { Text = "SLOGANS", ForeColor = Color.Lime, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10, FontStyle.Bold) };
            currentY += slogansLabel.Height + 10;

            slogansListBox = new CheckedListBox { Location = new Point(sidebarPadding, currentY), Width = controlWidth, Height = 120, Font = new Font("Segoe UI", 10) };
            currentY += slogansListBox.Height + 10;

            var gridControlLabel = new Label { Text = "Grid Control", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10, FontStyle.Bold) };
            currentY += gridControlLabel.Height + 10;

            var trainNoLabel = new Label { Text = "Train No", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth };
            currentY += trainNoLabel.Height + 10;

            trainNoTextBox = new TextBox { Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10) };
            currentY += trainNoTextBox.Height + 10;

            // LOAD, CLEAR, REFRESH buttons in a single row
            loadButton = new Button { Text = "LOAD", Location = new Point(sidebarPadding, currentY), Width = tripleButtonWidth, Height = 36 };
            clearButton = new Button { Text = "CLEAR", Location = new Point(sidebarPadding + tripleButtonWidth + buttonSpacing, currentY), Width = tripleButtonWidth, Height = 36 };
            refreshButton = new Button { Text = "REFRESH", Location = new Point(sidebarPadding + 2 * (tripleButtonWidth + buttonSpacing), currentY), Width = tripleButtonWidth + 50, Height = 36 };
            ButtonStyler.ApplyStandardStyle(loadButton, "primary", "small", 100);
            ButtonStyler.ApplyStandardStyle(clearButton, "secondary", "small", 100);
            ButtonStyler.ApplyStandardStyle(refreshButton, "info", "small", 100);
            currentY += loadButton.Height + 10;

            statusLabel = new Label { Text = "Status", ForeColor = Color.Lime, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10, FontStyle.Bold) };
            currentY += statusLabel.Height + 10;

            // Add column behavior information
            var columnInfoLabel = new Label
            {
                Text = "Column Info:\n• SAT/SDT: Read-only\n• Late/EAT/EDT: Right-click to update\n• Others: Auto-update on change",
                ForeColor = Color.White,
                Location = new Point(sidebarPadding, currentY),
                Width = controlWidth,
                Font = new Font("Segoe UI", 9),
                Height = 80
            };
            currentY += columnInfoLabel.Height + 10;

            // Current Station Info
            var currentStationLabel = new Label { Text = "Current Station", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10, FontStyle.Bold) };
            currentY += currentStationLabel.Height + 5;

            var stationInfoLabel = new Label
            {
                Text = currentStation != null ? $"{currentStation.StationName} ({currentStation.StationCode})" : "No current station",
                ForeColor = Color.Lime,
                Location = new Point(sidebarPadding, currentY),
                Width = controlWidth,
                Font = new Font("Segoe UI", 9),
                Height = 30
            };
            currentY += stationInfoLabel.Height + 10;

            var refreshConfigLabel = new Label { Text = "Auto Refresh Config (from Current Station)", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10, FontStyle.Bold) };
            currentY += refreshConfigLabel.Height + 10;

            var autoRefreshCheckBox = new CheckBox { Text = "Enable Auto Refresh", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Checked = autoRefreshEnabled, Enabled = false };
            currentY += autoRefreshCheckBox.Height + 5;

            var autoRefreshNoteLabel = new Label { Text = "(Configured in Current Station Settings)", ForeColor = Color.Gray, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 8) };
            currentY += autoRefreshNoteLabel.Height + 10;

            var refreshIntervalLabel = new Label { Text = "Interval (mins)", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth };
            currentY += refreshIntervalLabel.Height + 10;

            var refreshIntervalUpDown = new NumericUpDown { Location = new Point(sidebarPadding, currentY), Width = controlWidth, Minimum = 1, Maximum = 60, Value = refreshIntervalMinutes, Enabled = false };
            currentY += refreshIntervalUpDown.Height + 5;

            var intervalNoteLabel = new Label { Text = "(Configured in Current Station Settings)", ForeColor = Color.Gray, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 8) };
            currentY += intervalNoteLabel.Height + 10;

            // Auto Load/Delete Status
            var autoLoadDeleteLabel = new Label { Text = "Auto Load/Delete Status", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth, Font = new Font("Segoe UI", 10, FontStyle.Bold) };
            currentY += autoLoadDeleteLabel.Height + 5;

            var autoLoadStatusLabel = new Label
            {
                Text = currentStation != null ? $"Auto Load: {(currentStation.AutoLoad ? "Enabled" : "Disabled")} ({currentStation.AutoLoadInterval} mins)" : "Auto Load: Not configured",
                ForeColor = currentStation?.AutoLoad == true ? Color.Lime : Color.Orange,
                Location = new Point(sidebarPadding, currentY),
                Width = controlWidth,
                Font = new Font("Segoe UI", 9)
            };
            currentY += autoLoadStatusLabel.Height + 5;

            var autoDeleteStatusLabel = new Label
            {
                Text = currentStation != null ? $"Auto Delete: {(currentStation.AutoDelete ? "Enabled" : "Disabled")} ({currentStation.AutoDeleteInterval} mins)" : "Auto Delete: Not configured",
                ForeColor = currentStation?.AutoDelete == true ? Color.Lime : Color.Orange,
                Location = new Point(sidebarPadding, currentY),
                Width = controlWidth,
                Font = new Font("Segoe UI", 9)
            };
            currentY += autoDeleteStatusLabel.Height + 10;

            // Refresh Configuration Button
            var refreshConfigButton = new Button { Text = "Refresh Station Config", Location = new Point(sidebarPadding, currentY), Width = controlWidth, Height = 30 };
            ButtonStyler.ApplyStandardStyle(refreshConfigButton, "info", "medium", 300);
            refreshConfigButton.Click += async (s, e) => await RefreshCurrentStationConfiguration();
            currentY += refreshConfigButton.Height + 10;

            var loadModeLabel = new Label { Text = "Load Mode", ForeColor = Color.White, Location = new Point(sidebarPadding, currentY), Width = controlWidth };
            currentY += loadModeLabel.Height + 10;

            var loadModeComboBox = new ComboBox { Location = new Point(sidebarPadding, currentY), Width = controlWidth, DropDownStyle = ComboBoxStyle.DropDownList };
            loadModeComboBox.Items.AddRange(new object[] { "Current Day & Time", "Current Day Only", "All Trains" });
            loadModeComboBox.SelectedIndex = 0;
            loadModeComboBox.SelectedIndexChanged += (s, e) =>
            {
                switch (loadModeComboBox.SelectedIndex)
                {
                    case 0: loadTrainsForCurrentDayAndTime = true; break;
                    case 1: loadTrainsForCurrentDayAndTime = false; break;
                    case 2: loadTrainsForCurrentDayAndTime = false; break;
                }
                try
                {
                    trainService.ClearAllOnlineTrains();
                    LoadTrainData();
                }
                catch (Exception ex)
                {
                    toast.ShowError($"Error switching load mode: {ex.Message}");
                }
            };
            currentY += loadModeComboBox.Height + 10;

            rightSidebar.Controls.AddRange(new Control[] {
                repeatLabel, repeatCountUpDown,
                stopButton, playButton, pauseButton,
                volumeLabel, volumeTrackBar,
                slogansLabel, slogansListBox,
                gridControlLabel, trainNoLabel, trainNoTextBox,
                loadButton, clearButton, refreshButton,
                statusLabel,
                columnInfoLabel,
                currentStationLabel, stationInfoLabel,
                refreshConfigLabel, autoRefreshCheckBox, autoRefreshNoteLabel,
                refreshIntervalLabel, refreshIntervalUpDown, intervalNoteLabel,
                autoLoadDeleteLabel, autoLoadStatusLabel, autoDeleteStatusLabel,
                refreshConfigButton,
                loadModeLabel, loadModeComboBox
            });

            this.Controls.Add(rightSidebar);
        }

        private void InitializeEventHandlers()
        {
            trainGrid.CellClick += TrainGrid_CellClick;
            trainGrid.CellValueChanged += TrainGrid_CellValueChanged;
            trainGrid.DataError += TrainGrid_DataError;
            trainGrid.CellFormatting += TrainGrid_CellFormatting;
            trainGrid.CellMouseClick += TrainGrid_CellMouseClick;
            loadButton.Click += LoadButton_Click;
            clearButton.Click += ClearButton_Click;
            refreshButton.Click += RefreshButton_Click;
            playButton.Click += PlayButton_Click;
            pauseButton.Click += PauseButton_Click;
            stopButton.Click += StopButton_Click;
            volumeTrackBar.ValueChanged += VolumeTrackBar_ValueChanged;
            slogansListBox.ItemCheck += SlogansListBox_ItemCheck;
        }

        private async void LoadTrainData()
        {
            LogDebug("LoadTrainData: Starting data load");

            // Debug: Check database contents
            CheckDatabaseContents();

            isDataBinding = true;

            trainGrid.CellValueChanged -= TrainGrid_CellValueChanged;
            trainGrid.CellClick -= TrainGrid_CellClick;

            trainGrid.DataSource = null;
            trainGrid.Rows.Clear();

            try
            {
                // Load dynamic status options from announcement templates
                await LoadDynamicStatusOptions();

                // ALWAYS clear the online trains table first to ensure fresh data
                LogDebug("LoadTrainData: Clearing Online_Trains table");
                trainService.ClearAllOnlineTrains();

                // First, get filtered trains from master data
                DataTable filteredTrains = null;
                string loadMode = "";

                LogDebug($"LoadTrainData: Current load mode setting: {loadTrainsForCurrentDayAndTime}");

                if (loadTrainsForCurrentDayAndTime)
                {
                    // Load trains for current day and time window (30 minutes ago to end of day)
                    LogDebug("LoadTrainData: Calling GetTrainsForCurrentDayAndTime() - loading future trains from 30 mins ago to end of day");
                    filteredTrains = trainService.GetTrainsForCurrentDayAndTime(250);
                    loadMode = "current day and future trains (30 mins ago to end of day)";
                }
                else
                {
                    // Load all trains and filter for current day
                    LogDebug("LoadTrainData: Calling GetAllTrains()");
                    filteredTrains = trainService.GetAllTrains();
                    LogDebug($"LoadTrainData: Retrieved {filteredTrains?.Rows.Count ?? 0} trains from GetAllTrains");
                    filteredTrains = FilterTrainsForCurrentDay(filteredTrains);
                    loadMode = "all trains (filtered for current day)";
                }

                LogDebug($"LoadTrainData: Retrieved {filteredTrains?.Rows.Count ?? 0} trains using {loadMode} filter");

                if (filteredTrains != null && filteredTrains.Rows.Count > 0)
                {
                    LogDebug("LoadTrainData: Found filtered trains, adding to online list");

                    // Add filtered trains to online list
                    await AddFilteredTrainsToOnlineList(filteredTrains);

                    // Now load from Online_Trains table (which has the correct column structure)
                    LogDebug("LoadTrainData: Loading from Online_Trains table");
                    var onlineTrains = trainService.GetOnlineTrains();
                    LogDebug($"LoadTrainData: Retrieved {onlineTrains?.Rows.Count ?? 0} trains from Online_Trains table");

                    if (onlineTrains != null && onlineTrains.Rows.Count > 0)
                    {
                        // Debug: Show column names
                        LogDebug("Online_Trains table columns:");
                        foreach (DataColumn col in onlineTrains.Columns)
                        {
                            LogDebug($"  Column: {col.ColumnName}, Type: {col.DataType}");
                        }

                        // Debug: Show first row data
                        if (onlineTrains.Rows.Count > 0)
                        {
                            var firstRow = onlineTrains.Rows[0];
                            LogDebug("First row data:");
                            foreach (DataColumn col in onlineTrains.Columns)
                            {
                                LogDebug($"  {col.ColumnName}: {firstRow[col.ColumnName]}");
                            }
                        }

                        trainGrid.DataSource = onlineTrains;
                        LogDebug("LoadTrainData: Data bound to grid");

                        EnsureComboBoxValuesPresent("Status");
                        EnsureComboBoxValuesPresent("AD");
                    }
                    else
                    {
                        LogDebug("LoadTrainData: No trains found in Online_Trains table");
                    }
                }
                else
                {
                    LogDebug("LoadTrainData: No filtered trains found");
                }

                UpdateStatus($"Train data loaded successfully ({filteredTrains?.Rows.Count ?? 0} trains for {loadMode}) - {GetCurrentTimeWindowInfo()}");
            }
            catch (Exception ex)
            {
                LogDebug($"Error loading train data: {ex.Message}");
                LogDebug($"Stack trace: {ex.StackTrace}");
                toast.ShowError($"Error loading train data: {ex.Message}");
            }
            finally
            {
                trainGrid.CellValueChanged += TrainGrid_CellValueChanged;
                trainGrid.CellClick += TrainGrid_CellClick;
                isDataBinding = false;
            }
        }

        private async Task AddFilteredTrainsToOnlineList(DataTable filteredTrains)
        {
            using var onlineTrainLogger = BatchLoggerExtensions.CreateTrainDataLoader();

            try
            {
                LogDebug($"Adding {filteredTrains.Rows.Count} filtered trains to online list");
                onlineTrainLogger.LogStep($"Starting bulk addition of {filteredTrains.Rows.Count} trains to online list");

                // Suppress verbose logging during bulk operation
                Logger.SetVerboseLogging(false);

                int addedCount = 0;

                foreach (DataRow trainRow in filteredTrains.Rows)
                {
                    try
                    {
                        string trainNo = trainRow["Train_No"].ToString();
                        LogDebug($"Processing train {trainNo}");

                        // Add train to online list with default values and station code prefix
                        string trainNameWithPrefix = GetTrainNameWithStationCode(trainNo);
                        trainService.AddOnlineTrain(
                            trainNo,
                            trainNameWithPrefix,
                            trainRow["Train_AD"].ToString(),
                            "RUNNING RIGHT TIME", // Default status
                            "00:00", // Default late time
                            trainRow["Sch_AT"].ToString(), // Expected arrival time
                            trainRow["Sch_DT"].ToString(), // Expected departure time
                            trainRow["Sch_PF"].ToString(), // Platform
                            "False" // Default announcement flag
                        );

                        LogDebug($"Added train {trainNo} to online list");
                        onlineTrainLogger.IncrementSuccess();
                        addedCount++;
                    }
                    catch (Exception trainEx)
                    {
                        string trainNo = trainRow["Train_No"]?.ToString() ?? "Unknown";
                        LogDebug($"Error adding train {trainNo}: {trainEx.Message}");
                        onlineTrainLogger.IncrementFailure($"Failed to add train {trainNo}: {trainEx.Message}");
                    }
                }

                LogDebug($"Finished adding filtered trains to online list. Added: {addedCount}");
            }
            catch (Exception ex)
            {
                LogDebug($"Error adding filtered trains to online list: {ex.Message}");
                onlineTrainLogger.LogFailure("Bulk online train addition", ex.Message);
                throw;
            }
            finally
            {
                // Re-enable verbose logging
                Logger.SetVerboseLogging(true);
            }
        }

        private DataTable FilterTrainsForCurrentDay(DataTable allTrains)
        {
            try
            {
                // Get current day of week
                DayOfWeek currentDay = DateTime.Now.DayOfWeek;
                string dayColumn = GetDayColumn(currentDay);

                // Filter trains that run on current day
                DataTable filteredTrains = allTrains.Clone();
                foreach (DataRow row in allTrains.Rows)
                {
                    bool allDays = Convert.ToBoolean(row["All_Days"]);
                    bool currentDayEnabled = Convert.ToBoolean(row[dayColumn]);

                    if (allDays || currentDayEnabled)
                    {
                        filteredTrains.ImportRow(row);
                    }
                }

                return filteredTrains;
            }
            catch (Exception ex)
            {
                LogDebug($"Error filtering trains for current day: {ex.Message}");
                return allTrains; // Return original table if filtering fails
            }
        }

        private string GetDayColumn(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "Chk_Sun";
                case DayOfWeek.Monday: return "Chk_Mon";
                case DayOfWeek.Tuesday: return "Chk_Tue";
                case DayOfWeek.Wednesday: return "Chk_Wed";
                case DayOfWeek.Thursday: return "Chk_Thu";
                case DayOfWeek.Friday: return "Chk_Fri";
                case DayOfWeek.Saturday: return "Chk_Sat";
                default: return "Chk_Mon";
            }
        }

        private async Task LoadDynamicStatusOptions()
        {
            try
            {
                // Get active announcement templates for both A and D
                var allTemplates = await announcementTemplateService.GetActiveTemplatesAsync();

                // Get the Status column
                var statusCol = trainGrid.Columns["Status"] as DataGridViewComboBoxColumn;
                if (statusCol != null)
                {
                    statusCol.Items.Clear();

                    // Add template names as status options
                    foreach (var template in allTemplates)
                    {
                        statusCol.Items.Add(template.Name);
                    }

                    LogDebug($"Loaded {allTemplates.Count()} dynamic status options from announcement templates");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error loading dynamic status options: {ex.Message}");
                // Fallback to default status options if templates fail to load
                var statusCol = trainGrid.Columns["Status"] as DataGridViewComboBoxColumn;
                if (statusCol != null)
                {
                    statusCol.Items.Clear();
                    statusCol.Items.AddRange(new object[] {
                        "IS ARRIVING ON",
                        "HAS ARRIVED ON",
                        "WILL ARRIVE SHORTLY",
                        "RUNNING RIGHT TIME",
                        "RUNNING LATE",
                        "INDEFINITE LATE",
                        "CANCELLED",
                        "PLATFORM CHANGED",
                        "DIVERTED",
                        "TERMINATED",
                        "DEPARTING", // <-- Make sure this is included!
                        "WILL DEPART SHORTLY",
                        "HAS DEPARTED",
                        "DEPARTURE DELAYED"
                    });
                }
            }
        }

        private async Task LoadDynamicStatusOptionsForAD(string arrivalDeparture)
        {
            try
            {
                // Get active announcement templates filtered by A/D
                var templates = await announcementTemplateService.GetActiveTemplatesByArrivalDepartureAsync(arrivalDeparture);

                // Get the Status column
                var statusCol = trainGrid.Columns["Status"] as DataGridViewComboBoxColumn;
                if (statusCol != null)
                {
                    statusCol.Items.Clear();

                    // Add template names as status options
                    foreach (var template in templates)
                    {
                        statusCol.Items.Add(template.Name);
                    }

                    LogDebug($"Loaded {templates.Count()} dynamic status options for {arrivalDeparture} from announcement templates");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error loading dynamic status options for {arrivalDeparture}: {ex.Message}");
                // Fallback to default status options if templates fail to load
                var statusCol = trainGrid.Columns["Status"] as DataGridViewComboBoxColumn;
                if (statusCol != null)
                {
                    statusCol.Items.Clear();
                    if (arrivalDeparture == "A")
                    {
                        statusCol.Items.AddRange(new object[] {
                            "IS ARRIVING ON",
                            "HAS ARRIVED ON",
                            "WILL ARRIVE SHORTLY",
                            "RUNNING RIGHT TIME",
                            "RUNNING LATE",
                            "INDEFINITE LATE",
                            "CANCELLED",
                            "PLATFORM CHANGED",
                            "DIVERTED",
                            "TERMINATED"
                        });
                    }
                    else if (arrivalDeparture == "D")
                    {
                        statusCol.Items.AddRange(new object[] {
                            "DEPARTING",
                            "WILL DEPART SHORTLY",
                            "HAS DEPARTED",
                            "DEPARTURE DELAYED"
                        });
                    }
                }
            }
        }

        private void EnsureComboBoxValuesPresent(string columnName)
        {
            if (!(trainGrid.Columns[columnName] is DataGridViewComboBoxColumn comboCol)) return;
            var items = new HashSet<string>();
            foreach (var obj in comboCol.Items) items.Add(obj.ToString());

            // Special handling for platform columns - ensure current station platforms are available
            if (columnName == "PF" || columnName == "ChangedPF")
            {
                var currentPlatforms = GetCurrentStationPlatforms();
                foreach (var platform in currentPlatforms)
                {
                    if (!items.Contains(platform))
                    {
                        comboCol.Items.Add(platform);
                        items.Add(platform);
                    }
                }
            }

            foreach (DataGridViewRow row in trainGrid.Rows)
            {
                if (row.IsNewRow) continue;
                var val = row.Cells[columnName].Value?.ToString();
                if (!string.IsNullOrEmpty(val) && !items.Contains(val))
                {
                    comboCol.Items.Add(val);
                    items.Add(val);
                }
            }
        }

        private void LoadAdvertisingData()
        {
            try
            {
                var advertisements = advertisingService.GetAllAdvertisements();
                slogansListBox.Items.Clear();
                foreach (DataRow row in advertisements.Rows)
                {
                    // Only add slogans (Ann_Type == 'Slogans')
                    if (row["Ann_Type"]?.ToString() == "Slogans")
                    {
                        slogansListBox.Items.Add(row["Adver_Name"].ToString());
                    }
                }
                UpdateStatus("Advertising data loaded successfully");
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading advertising data: {ex.Message}");
            }
        }

        private void TrainGrid_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex == trainGrid.Columns["DEL"].Index)
            {
                var trainNo = trainGrid.Rows[e.RowIndex].Cells["TrainNumber"].Value.ToString();
                if (MessageBox.Show($"Are you sure you want to remove train {trainNo} from online list?", "Confirm Delete",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    try
                    {
                        // Delete only from Online_Trains table
                        trainService.DeleteOnlineTrain(trainNo);
                        // Clear the grid and reload data
                        trainGrid.DataSource = null;
                        trainGrid.Rows.Clear();
                        LoadTrainData();
                        toast.ShowSuccess("Train removed from online list");
                    }
                    catch (Exception ex)
                    {
                        toast.ShowError($"Error removing train from online list: {ex.Message}");
                    }
                }
            }
        }

        private async void TrainGrid_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (isDataBinding) return;

            if (e.RowIndex < 0 || e.ColumnIndex < 0 ||
                e.ColumnIndex == trainGrid.Columns["SNo"].Index ||
                e.ColumnIndex == trainGrid.Columns["DEL"].Index) return;

            var row = trainGrid.Rows[e.RowIndex];
            if (row == null) return;

            try
            {
                string trainNumber = row.Cells["TrainNumber"].Value?.ToString();
                string columnName = trainGrid.Columns[e.ColumnIndex].Name;
                LogDebug($"Cell value changed for online train {trainNumber} at column {columnName}");

                // Handle Status column change - update conditional editing
                if (e.ColumnIndex == trainGrid.Columns["Status"].Index)
                {
                    string newStatus = row.Cells["Status"].Value?.ToString();
                    UpdateConditionalEditing(row, newStatus);
                }

                // Handle A/D column change - update status options for only this row
                if (e.ColumnIndex == trainGrid.Columns["AD"].Index)
                {
                    string newAD = row.Cells["AD"].Value?.ToString();
                    if (!string.IsNullOrEmpty(newAD) && (newAD == "A" || newAD == "D"))
                    {
                        // Fetch status options dynamically from announcement template table
                        var statusOptions = new List<string>();
                        if (newAD == "A")
                        {
                            var templates = await announcementTemplateService.GetActiveTemplatesByArrivalDepartureAsync("A");
                            statusOptions = templates.Select(t => t.Name).ToList();
                        }
                        else // D
                        {
                            var templates = await announcementTemplateService.GetActiveTemplatesByArrivalDepartureAsync("D");
                            statusOptions = templates.Select(t => t.Name).ToList();
                        }
                        if (statusOptions.Count == 0)
                        {
                            // Fallback to hardcoded if none found
                            statusOptions = newAD == "A"
                                ? new List<string> { "IS ARRIVING ON", "HAS ARRIVED ON", "WILL ARRIVE SHORTLY", "RUNNING RIGHT TIME", "RUNNING LATE", "INDEFINITE LATE", "CANCELLED", "PLATFORM CHANGED", "DIVERTED", "TERMINATED" }
                                : new List<string> { "DEPARTING", "WILL DEPART SHORTLY", "HAS DEPARTED", "DEPARTURE DELAYED" };
                        }
                        var statusCell = new DataGridViewComboBoxCell();
                        statusCell.Items.AddRange(statusOptions.ToArray());
                        // Set default value
                        if (newAD == "D" && statusOptions.Contains("DEPARTING"))
                            statusCell.Value = "DEPARTING";
                        else if (statusOptions.Count > 0)
                            statusCell.Value = statusOptions[0];
                        row.Cells["Status"] = statusCell;
                    }
                }

                // Handle Changed PF column validation
                if (e.ColumnIndex == trainGrid.Columns["ChangedPF"].Index)
                {
                    string newChangedPF = row.Cells["ChangedPF"].Value?.ToString() ?? "";
                    string originalPF = row.Cells["PF"].Value?.ToString() ?? "";

                    if (!string.IsNullOrEmpty(newChangedPF) && newChangedPF == originalPF)
                    {
                        MessageBox.Show("Changed Platform must be different from the original platform.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        row.Cells["ChangedPF"].Value = "";
                        return;
                    }
                }

                // Check if the changed column should trigger a database update
                // Exclude Late, EAT, and EDT columns from auto-updates
                if (columnName == "Late" || columnName == "EAT" || columnName == "EDT")
                {
                    LogDebug($"Column {columnName} changed but excluded from auto-update");
                    return; // Don't update database for these columns
                }

                // Get values from the grid for allowed columns only
                string trainName = row.Cells["TrainName"].Value?.ToString() ?? "";
                string status = row.Cells["Status"].Value?.ToString() ?? "";
                string pf = row.Cells["PF"].Value?.ToString() ?? "";
                string changedPF = row.Cells["ChangedPF"].Value?.ToString() ?? "";
                string divertedFrom = row.Cells["DivertedFrom"].Value?.ToString() ?? "";

                // Handle AN column value
                string an = "False";
                if (row.Cells["AN"] is DataGridViewCheckBoxCell checkBoxCell)
                {
                    if (checkBoxCell.Value != null)
                    {
                        if (checkBoxCell.Value is bool boolValue)
                        {
                            an = boolValue ? "True" : "False";
                        }
                        else if (checkBoxCell.Value is CheckState checkState)
                        {
                            an = (checkState == CheckState.Checked) ? "True" : "False";
                        }
                        else
                        {
                            an = Convert.ToString(checkBoxCell.Value) ?? "False";
                        }
                    }
                }

                // Get current values for excluded columns from database to preserve them
                var currentOnlineTrain = trainService.GetOnlineTrainByNumber(trainNumber);
                string late = currentOnlineTrain?["Late"]?.ToString() ?? "00:00";
                string eat = currentOnlineTrain?["Exp_AT"]?.ToString() ?? "";
                string edt = currentOnlineTrain?["Exp_DT"]?.ToString() ?? "";

                // Update only in Online_Trains table with preserved values for excluded columns
                trainService.UpdateOnlineTrain(
                    trainNumber,
                    trainName,
                    status,
                    late,  // Preserve current value from database
                    eat,   // Preserve current value from database
                    edt,   // Preserve current value from database
                    pf,
                    an,
                    changedPF,
                    divertedFrom
                );
                UpdateStatus($"Online train updated successfully (column: {columnName})");
            }
            catch (Exception ex)
            {
                LogDebug($"Error in TrainGrid_CellValueChanged: {ex.Message}");
                LogDebug($"Stack trace: {ex.StackTrace}");
                toast.ShowError($"Error updating online train: {ex.Message}");
            }
        }

        private void UpdateConditionalEditing(DataGridViewRow row, string status)
        {
            try
            {
                // Enable/disable Changed PF column based on status
                bool enableChangedPF = status == "PLATFORM CHANGED";
                row.Cells["ChangedPF"].ReadOnly = !enableChangedPF;

                // Enable/disable Diverted From column based on status
                bool enableDivertedFrom = status == "DIVERTED";
                row.Cells["DivertedFrom"].ReadOnly = !enableDivertedFrom;

                // Clear values when disabling
                if (!enableChangedPF)
                {
                    row.Cells["ChangedPF"].Value = "";
                }

                if (!enableDivertedFrom)
                {
                    row.Cells["DivertedFrom"].Value = "";
                }

                LogDebug($"Updated conditional editing for status '{status}': ChangedPF={enableChangedPF}, DivertedFrom={enableDivertedFrom}");
            }
            catch (Exception ex)
            {
                LogDebug($"Error updating conditional editing: {ex.Message}");
            }
        }

        private void LoadButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(trainNoTextBox.Text))
            {
                toast.ShowError("Please enter a train number");
                return;
            }

            try
            {
                // First check if train exists in master data
                var masterTrain = trainService.GetTrainByNumber(trainNoTextBox.Text);
                if (masterTrain == null)
                {
                    toast.ShowError("Train not found in master data");
                    return;
                }

                // Check if train already exists in online trains
                var onlineTrain = trainService.GetOnlineTrainByNumber(trainNoTextBox.Text);
                if (onlineTrain != null)
                {
                    toast.ShowError("Train is already in online list");
                    return;
                }

                // Check if train runs on current day
                DayOfWeek currentDay = DateTime.Now.DayOfWeek;
                string dayColumn = GetDayColumn(currentDay);
                bool allDays = Convert.ToBoolean(masterTrain["All_Days"]);
                bool currentDayEnabled = Convert.ToBoolean(masterTrain[dayColumn]);

                if (!allDays && !currentDayEnabled)
                {
                    toast.ShowError($"Train {trainNoTextBox.Text} does not run on {currentDay}");
                    return;
                }

                // Add train to online trains using master data with station code prefix
                string trainNameWithPrefix = GetTrainNameWithStationCode(masterTrain["Train_No"].ToString());
                trainService.AddOnlineTrain(
                    masterTrain["Train_No"].ToString(),
                    trainNameWithPrefix,
                    masterTrain["Train_AD"].ToString(),
                    "RUNNING RIGHT TIME",
                    "00:00",
                    masterTrain["Sch_AT"].ToString(),
                    masterTrain["Sch_DT"].ToString(),
                    masterTrain["Sch_PF"].ToString(),
                    "False",
                    "", // Changed PF - empty by default
                    "" // Diverted From - empty by default
                );

                LoadTrainData(); // Refresh the grid
                UpdateStatus($"Train {trainNoTextBox.Text} added to online list");
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error adding train to online list: {ex.Message}");
                LogDebug($"Exception in LoadButton_Click: {ex.Message}\nStackTrace: {ex.StackTrace}");
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            trainNoTextBox.Clear();
            trainGrid.Rows.Clear();
            UpdateStatus("Grid cleared");
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            PerformIncrementalRefresh();
            LoadAdvertisingData();
            UpdateStatus("Manual refresh completed");
        }

        private async void RefreshStatusOptions()
        {
            try
            {
                await LoadDynamicStatusOptions();
                UpdateStatus("Status options refreshed from templates");
            }
            catch (Exception ex)
            {
                LogDebug($"Error refreshing status options: {ex.Message}");
                UpdateStatus("Error refreshing status options");
            }
        }

        private async void PlayButton_Click(object sender, EventArgs e)
        {
            if (audioPlayer.IsPlaying)
            {
                audioPlayer.ResumePlayback();
            }
            else
            {
                // Check if any train announcements are checked
                bool anyTrainChecked = false;
                foreach (DataGridViewRow row in trainGrid.Rows)
                {
                    if (row.IsNewRow) continue;
                    var anValue = row.Cells["AN"].Value;
                    if (anValue != null && anValue != DBNull.Value && Convert.ToBoolean(anValue))
                    {
                        anyTrainChecked = true;
                        break;
                    }
                }
                // If no train checked, but at least one slogan checked, play only slogans
                if (!anyTrainChecked && slogansListBox.CheckedItems.Count > 0)
                {
                    PlayOnlySlogans();
                }
                else
                {
                    await StartPlayback();
                }
            }
        }

        private void PauseButton_Click(object sender, EventArgs e)
        {
            if (audioPlayer.IsPlaying)
            {
                audioPlayer.PausePlayback();
            }
        }

        private void StopButton_Click(object sender, EventArgs e)
        {
            audioPlayer.StopPlayback();
        }

        private void VolumeTrackBar_ValueChanged(object sender, EventArgs e)
        {
            // Update volume in real-time
            int volume = volumeTrackBar.Value;
            audioPlayer.SetVolume(volume);
            UpdateStatus($"Volume set to {volume}%");
        }

        private void SlogansListBox_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            // Implement slogan selection
            var slogan = slogansListBox.Items[e.Index].ToString();
            UpdateStatus($"Slogan {(e.NewValue == CheckState.Checked ? "selected" : "unselected")}: {slogan}");
        }

        private void TrainGrid_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // Handle data grid view data errors
            Console.WriteLine($"DataGridView Data Error: {e.Exception.Message} for Row: {e.RowIndex}, Column: {e.ColumnIndex}");
            if (e.Context == DataGridViewDataErrorContexts.Commit &&
                e.Exception is ArgumentException &&
                e.ColumnIndex >= 0 &&
                trainGrid.Columns[e.ColumnIndex] is DataGridViewComboBoxColumn)
            {
                string columnName = trainGrid.Columns[e.ColumnIndex].Name;
                object invalidValue = trainGrid.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
                toast.ShowError($"Invalid value '{invalidValue}' for {columnName} column. Please select a valid option.");
                Console.WriteLine($"DEBUG - Invalid ComboBox value. Column: {columnName}, Value: {invalidValue}");
            }
            else
            {
                toast.ShowError($"Data error: {e.Exception.Message}");
            }
            e.ThrowException = false; // Suppress the error
        }

        private void TrainGrid_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.Value == null) return;

            // Format time columns (SAT, SDT, EAT, EDT)
            if (e.ColumnIndex == trainGrid.Columns["SAT"].Index ||
                e.ColumnIndex == trainGrid.Columns["SDT"].Index ||
                e.ColumnIndex == trainGrid.Columns["EAT"].Index ||
                e.ColumnIndex == trainGrid.Columns["EDT"].Index)
            {
                if (e.Value is DateTime time)
                {
                    e.Value = time.ToString("HH:mm");
                    e.FormattingApplied = true;
                }
            }

            // Format read-only columns (SAT, SDT) with gray background
            if (e.ColumnIndex == trainGrid.Columns["SAT"].Index ||
                e.ColumnIndex == trainGrid.Columns["SDT"].Index)
            {
                e.CellStyle.BackColor = Color.FromArgb(240, 240, 240); // Light gray
                e.CellStyle.ForeColor = Color.Gray;
                e.CellStyle.Font = new Font(trainGrid.Font, FontStyle.Italic);
            }

            // Format columns excluded from auto-updates (Late, EAT, EDT) with light yellow background
            if (e.ColumnIndex == trainGrid.Columns["Late"].Index ||
                e.ColumnIndex == trainGrid.Columns["EAT"].Index ||
                e.ColumnIndex == trainGrid.Columns["EDT"].Index)
            {
                e.CellStyle.BackColor = Color.FromArgb(255, 255, 220); // Light yellow
            }

            // Format status column with colors
            if (e.ColumnIndex == trainGrid.Columns["Status"].Index)
            {
                string status = e.Value.ToString();
                switch (status)
                {
                    case "RUNNING LATE":
                    case "INDEFINITE LATE":
                        e.CellStyle.ForeColor = Color.Red;
                        break;
                    case "RUNNING RIGHT TIME":
                        e.CellStyle.ForeColor = Color.Green;
                        break;
                    case "CANCELLED":
                        e.CellStyle.ForeColor = Color.Red;
                        e.CellStyle.Font = new Font(trainGrid.Font, FontStyle.Bold);
                        break;
                    case "PLATFORM CHANGED":
                        e.CellStyle.ForeColor = Color.Orange;
                        e.CellStyle.Font = new Font(trainGrid.Font, FontStyle.Bold);
                        break;
                    case "DIVERTED":
                        e.CellStyle.ForeColor = Color.Purple;
                        e.CellStyle.Font = new Font(trainGrid.Font, FontStyle.Bold);
                        break;
                }
            }

            // Format delete button column with danger styling
            if (e.ColumnIndex == trainGrid.Columns["DEL"].Index)
            {
                e.CellStyle.BackColor = Color.FromArgb(220, 53, 69); // Danger red
                e.CellStyle.ForeColor = Color.White;
                e.CellStyle.Font = new Font(trainGrid.Font, FontStyle.Bold);
                e.CellStyle.SelectionBackColor = Color.FromArgb(200, 35, 51); // Darker red for selection
                e.CellStyle.SelectionForeColor = Color.White;
            }

            // Format conditional editing columns (Changed PF, Diverted From) with light blue background when enabled
            if (e.ColumnIndex == trainGrid.Columns["ChangedPF"].Index ||
                e.ColumnIndex == trainGrid.Columns["DivertedFrom"].Index)
            {
                var cell = trainGrid.Rows[e.RowIndex].Cells[e.ColumnIndex];
                if (!cell.ReadOnly)
                {
                    e.CellStyle.BackColor = Color.FromArgb(220, 240, 255); // Light blue when enabled
                    e.CellStyle.ForeColor = Color.Black;
                }
                else
                {
                    e.CellStyle.BackColor = Color.FromArgb(240, 240, 240); // Light gray when disabled
                    e.CellStyle.ForeColor = Color.Gray;
                }
            }
        }

        private void UpdateStatus(string message)
        {
            statusLabel.Text = message;
            toast.ShowInfo(message);
        }

        private async Task StartPlayback()
        {
            try
            {
                // Get repeat count from UI control
                int repeatCount = (int)repeatCountUpDown.Value;
                LogDebug($"Repeat count set to: {repeatCount}");

                // Build the audio queue first and get train information
                var (audioQueue, trainAnnouncements) = await BuildAudioQueueAsync();

                // Check if there are any audio files to play
                if (audioPlayer.TotalAudioFiles == 0)
                {
                    LogDebug("No audio files in queue to play");
                    UpdateStatus("No audio files to play");
                    return;
                }

                // Prepare logging information
                string announcementMessage;
                string detailsMessage;

                if (trainAnnouncements.Count > 0)
                {
                    // Format train announcements for logging with advertisement information
                    var trainInfoList = new List<string>();
                    foreach (var train in trainAnnouncements)
                    {
                        string trainInfo = $"{train.trainNo} - {train.status}";
                        if (train.advertisements.Count > 0)
                        {
                            trainInfo += $" with adv {string.Join(", ", train.advertisements)}";
                        }
                        trainInfoList.Add(trainInfo);
                    }
                    announcementMessage = string.Join(", ", trainInfoList);

                    detailsMessage = $"Started audio playback with {audioPlayer.TotalAudioFiles} files, repeat count: {repeatCount}, " +
                                   $"Languages: {string.Join(", ", (await GetActiveLanguagesAsync()).Select(l => l.Code))}, " +
                                   $"Slogans: {slogansListBox.CheckedItems.Count}";
                }
                else if (slogansListBox.CheckedItems.Count > 0)
                {
                    // Only slogans are being played
                    var slogans = slogansListBox.CheckedItems.Cast<string>().ToList();
                    announcementMessage = $"Slogans: {string.Join(", ", slogans)}";

                    detailsMessage = $"Started audio playback with {audioPlayer.TotalAudioFiles} files, repeat count: {repeatCount}, " +
                                   $"Languages: {string.Join(", ", (await GetActiveLanguagesAsync()).Select(l => l.Code))}";
                }
                else
                {
                    // Fallback for any other case
                    announcementMessage = "Audio Playback";
                    detailsMessage = $"Started audio playback with {audioPlayer.TotalAudioFiles} files, repeat count: {repeatCount}";
                }

                // Log announcement playback start
                Logger.LogAnnouncementMade(announcementMessage, detailsMessage);

                // Start playback using AudioPlayer utility
                audioPlayer.StartPlayback();
            }
            catch (Exception ex)
            {
                LogDebug($"Error in StartPlayback: {ex.Message}", true);
                UpdateStatus($"Error: {ex.Message}");

                // Log announcement playback error
                Logger.LogError(LogCategory.Announcement, "Audio playback failed",
                               $"Error: {ex.Message}", "AnnouncementBoardForm.StartPlayback", ex);

                audioPlayer.StopPlayback();
            }
        }



        private async Task<(List<string> audioQueue, List<(string trainNo, string status, List<string> advertisements)> trainAnnouncements)> BuildAudioQueueAsync()
        {
            var audioQueue = new List<string>();
            var trainAnnouncements = new List<(string trainNo, string status, List<string> advertisements)>();
            LogDebug("BuildAudioQueue: Starting to build audio queue");

            // Get active languages based on station preferences
            var activeLanguages = await GetActiveLanguagesAsync();
            LogDebug($"Found {activeLanguages.Count} active languages: {string.Join(", ", activeLanguages.Select(l => l.Code))}");

            if (activeLanguages.Count == 0)
            {
                LogDebug("No active languages found, using default ENGLISH");
                activeLanguages.Add(new Models.Language { Code = "EN", Name = "ENGLISH", WaveFolderPath = "ENGLISH" });
            }

            // Check if any train announcements are checked
            bool anyTrainChecked = false;
            foreach (DataGridViewRow row in trainGrid.Rows)
            {
                if (row.IsNewRow) continue;
                var anValue = row.Cells["AN"].Value;
                if (anValue != null && anValue != DBNull.Value && Convert.ToBoolean(anValue))
                {
                    anyTrainChecked = true;
                    break;
                }
            }
            // If no train checked, but at least one slogan checked, play only slogans (handled in PlayButton_Click)
            if (!anyTrainChecked && slogansListBox.CheckedItems.Count > 0)
            {
                // Do not build the normal queue
                return (audioQueue, trainAnnouncements);
            }

            // Add selected slogans to the queue for each language in preference order
            LogDebug($"Number of selected slogans: {slogansListBox.CheckedItems.Count}");
            foreach (var language in activeLanguages)
            {
                LogDebug($"Processing slogans for language: {language.Name} ({language.Code})");
                foreach (var item in slogansListBox.CheckedItems)
                {
                    string slogan = item.ToString();
                    var sloganFiles = GetAudioFilesForPart("Slogan", slogan, language);
                    if (sloganFiles.Count > 0)
                    {
                        audioQueue.AddRange(sloganFiles);
                        LogDebug($"Added slogan file to queue for {language.Code}: {slogan}");
                    }
                    else
                    {
                        LogDebug($"No audio file found for slogan in {language.Code}: {slogan}");
                    }
                }
            }

            // Get all advertisements once
            var allAds = advertisingService.GetAllAdvertisements();

            // Add train announcements to the queue for each language using template sequences
            foreach (DataGridViewRow row in trainGrid.Rows)
            {
                if (row.IsNewRow) continue;
                var anValue = row.Cells["AN"].Value;
                LogDebug($"Checking train row - AN value: {anValue}, Type: {anValue?.GetType()}");

                if (anValue != null && anValue != DBNull.Value && Convert.ToBoolean(anValue))
                {
                    string trainNo = row.Cells["TrainNumber"].Value?.ToString();
                    string status = row.Cells["Status"].Value?.ToString();
                    string platform = row.Cells["PF"].Value?.ToString();

                    // Collect advertisements for this train
                    var trainAdvertisements = new List<string>();

                    // --- ADVERTISEMENT LOGIC START ---
                    var adAudioBefore = new List<string>();
                    var adAudioAfter = new List<string>();

                    foreach (DataRow adRow in allAds.Rows)
                    {
                        var adTrainNumbers = (adRow["TrainNumber"]?.ToString() ?? "").Split(',').Select(t => t.Trim());
                        if (adTrainNumbers.Contains(trainNo))
                        {
                            string playPosition = adRow["PlayPosition"]?.ToString() ?? "Before";
                            string annType = adRow["Ann_Type"]?.ToString();
                            string adverName = adRow["Adver_Name"]?.ToString();

                            // Add advertisement info for logging (only if it's not a slogan)
                            if (annType != "Slogans")
                            {
                                trainAdvertisements.Add($"{adverName} {playPosition.ToLower()}");
                            }

                            var languageWaves = advertisingService.GetAdvertisementLanguageWaves(annType, adverName);
                            bool audioAdded = false;
                            foreach (var language in activeLanguages)
                            {
                                // Try both language.Name and language.Code for compatibility
                                string waveFile = null;
                                if (languageWaves.TryGetValue(language.Name, out waveFile) && !string.IsNullOrEmpty(waveFile))
                                {
                                    if (playPosition == "Before" || playPosition == "Both")
                                        adAudioBefore.Add(waveFile);
                                    if (playPosition == "After" || playPosition == "Both")
                                        adAudioAfter.Add(waveFile);
                                    audioAdded = true;
                                }
                                else if (languageWaves.TryGetValue(language.Code, out waveFile) && !string.IsNullOrEmpty(waveFile))
                                {
                                    if (playPosition == "Before" || playPosition == "Both")
                                        adAudioBefore.Add(waveFile);
                                    if (playPosition == "After" || playPosition == "Both")
                                        adAudioAfter.Add(waveFile);
                                    audioAdded = true;
                                }
                            }

                            // Increment quota usage if audio was added and this is an Advertising type
                            if (audioAdded && annType == "Advertising")
                            {
                                try
                                {
                                    advertisingService.IncrementQuotaUsed(annType, adverName);
                                    LogDebug($"Incremented quota usage for advertisement: {adverName}");
                                }
                                catch (Exception ex)
                                {
                                    LogDebug($"Error incrementing quota usage for {adverName}: {ex.Message}");
                                }
                            }
                        }
                    }
                    // --- ADVERTISEMENT LOGIC END ---

                    // Add train announcement info for logging
                    if (!string.IsNullOrEmpty(trainNo) && !string.IsNullOrEmpty(status))
                    {
                        trainAnnouncements.Add((trainNo, status, trainAdvertisements));
                    }

                    // 1. Add adAudioBefore to queue
                    audioQueue.AddRange(adAudioBefore);

                    // 2. Add main train announcement for each language
                    foreach (var language in activeLanguages)
                    {
                        LogDebug($"Building announcement for Train: {trainNo}, Status: {status}, Platform: {platform}, Language: {language.Name} ({language.Code})");
                        var playlist = await BuildAnnouncementFromTemplateSequence(trainNo, status, platform, language);
                        LogDebug($"Generated playlist with {playlist.Count} files for {language.Code}");
                        audioQueue.AddRange(playlist);
                    }

                    // 3. Add adAudioAfter to queue
                    audioQueue.AddRange(adAudioAfter);
                }
            }

            LogDebug($"Final audio queue size: {audioQueue.Count} files");
            if (audioQueue.Count == 0)
            {
                LogDebug("No audio files found in queue. Check if any trains are selected for announcement (AN column)");
                LogDebug("Please ensure: 1) At least one train has AN column checked 2) Audio files exist in the correct location");
            }

            // Set the audio queue in the AudioPlayer
            int repeatCount = (int)repeatCountUpDown.Value;
            audioPlayer.SetAudioQueue(audioQueue, repeatCount);

            return (audioQueue, trainAnnouncements);
        }

        private async Task<List<string>> BuildAnnouncementFromTemplateSequence(string trainNo, string status, string platform, Models.Language language)
        {
            var playlist = new List<string>();

            try
            {
                LogDebug($"Building announcement from template sequence for Train: {trainNo}, Status: {status}, Language: {language.Code}");

                // Find the template by status name
                var template = await announcementTemplateService.GetTemplateByNameAsync(status);
                if (template == null)
                {
                    LogDebug($"Template not found for status: {status}");
                    return playlist;
                }

                // Get the sequence for this template and language
                var sequence = await sequenceRepository.GetSequenceByTemplateAndLanguageAsync(template.Id, language.Id);
                if (sequence == null)
                {
                    LogDebug($"Sequence not found for template: {template.Name}, language: {language.Code}");
                    return playlist;
                }

                // Get sequence items ordered by OrderIndex
                var sequenceItems = await sequenceItemRepository.GetItemsBySequenceOrderedAsync(sequence.Id);
                if (sequenceItems == null || !sequenceItems.Any())
                {
                    LogDebug($"No sequence items found for sequence: {sequence.Name}");
                    return playlist;
                }

                // Process each sequence item using the same approach as Sequence Management
                foreach (var item in sequenceItems)
                {
                    if (item.Type == ItemType.AudioFile)
                    {
                        // Add audio file directly - using the same path handling as Sequence Management
                        string relativePath = item.Content;

                        // If the audioPath contains a complete path with "WAVE" folder, extract only the portion after WAVE
                        var waveIndex = item.Content.IndexOf("WAVE");
                        if (waveIndex >= 0)
                        {
                            relativePath = item.Content.Substring(waveIndex + 5); // "WAVE" is 4 characters + 1 for separator
                            relativePath = relativePath.TrimStart('\\', '/'); // Remove leading separators
                        }

                        var fullPath = Path.Combine(fullAudioPath, relativePath);
                        if (File.Exists(fullPath))
                        {
                            playlist.Add(fullPath);
                            LogDebug($"Added audio file: {relativePath}");
                        }
                        else
                        {
                            LogDebug($"Audio file not found: {fullPath}");
                        }
                    }
                    else if (item.Type == ItemType.Placeholder)
                    {
                        // Process placeholder with train data using the same approach as Sequence Management
                        LogDebug($"BuildAnnouncementFromTemplateSequence: Processing placeholder '{item.Content}' for train {trainNo}");
                        var placeholderFiles = ProcessPlaceholderWithTrainData(item.Content, trainNo, platform, language);
                        playlist.AddRange(placeholderFiles);
                        LogDebug($"Processed placeholder {item.Content} with {placeholderFiles.Count} files");
                    }
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error building announcement from template sequence: {ex.Message}");
            }

            return playlist;
        }

        private List<string> ProcessPlaceholderWithTrainData(string placeholder, string trainNo, string platform, Models.Language language)
        {
            var files = new List<string>();

            try
            {
                switch (placeholder.ToUpper())
                {
                    case "TRAIN_NUMBER":
                        // Add each digit of train number
                        foreach (char digit in trainNo)
                        {
                            var digitFiles = GetAudioFilesForPart("Train_No", digit.ToString(), language);
                            files.AddRange(digitFiles);
                        }
                        break;

                    case "TRAIN_NAME":
                        // Get train name from database
                        var trainData = trainService.GetTrainByNumber(trainNo);
                        if (trainData != null)
                        {
                            string trainName = trainData["Train_No"]?.ToString();
                            if (!string.IsNullOrEmpty(trainName))
                            {
                                var trainNameFiles = GetAudioFilesForPart("Train_Name", trainName, language);
                                files.AddRange(trainNameFiles);
                            }
                        }
                        break;

                    case "FROM_STATION":
                        // Get source station from database
                        var trainInfo = trainService.GetTrainByNumber(trainNo);
                        if (trainInfo != null)
                        {
                            string srcStnCode = trainInfo["Src_Stn"]?.ToString();
                            if (!string.IsNullOrEmpty(srcStnCode))
                            {
                                var stationFiles = GetAudioFilesForPart("City", srcStnCode, language);
                                files.AddRange(stationFiles);
                            }
                        }
                        break;

                    case "TO_STATION":
                        // Get destination station from database
                        var trainData2 = trainService.GetTrainByNumber(trainNo);
                        if (trainData2 != null)
                        {
                            string destStnCode = trainData2["Desti_Stn"]?.ToString();
                            if (!string.IsNullOrEmpty(destStnCode))
                            {
                                var stationFiles = GetAudioFilesForPart("City", destStnCode, language);
                                files.AddRange(stationFiles);
                            }
                        }
                        break;

                    case "PLATFORM_NUMBER":
                        // Add platform number digits
                        if (!string.IsNullOrEmpty(platform) && platform != "--" && platform != "  ")
                        {
                            foreach (char digit in platform)
                            {
                                var digitFiles = GetAudioFilesForPart("Platform", digit.ToString(), language);
                                files.AddRange(digitFiles);
                            }
                        }
                        break;

                    case "UPDATED_PLATFORM_NUMBER":
                        // Get updated platform from online train data
                        var onlineTrainForUpdatedPF = trainService.GetOnlineTrainByNumber(trainNo);
                        if (onlineTrainForUpdatedPF != null)
                        {
                            string updatedPlatform = onlineTrainForUpdatedPF["Changed_PF"]?.ToString();
                            if (!string.IsNullOrEmpty(updatedPlatform) && updatedPlatform != "--" && updatedPlatform != "  ")
                            {
                                foreach (char digit in updatedPlatform)
                                {
                                    var digitFiles = GetAudioFilesForPart("Platform", digit.ToString(), language);
                                    files.AddRange(digitFiles);
                                }
                            }
                        }
                        break;

                    case "DIVERTED_STATION":
                        // Get diverted station from online train data
                        var onlineTrainForDiverted = trainService.GetOnlineTrainByNumber(trainNo);
                        if (onlineTrainForDiverted != null)
                        {
                            string divertedStation = onlineTrainForDiverted["Diverted_From"]?.ToString();
                            if (!string.IsNullOrEmpty(divertedStation))
                            {
                                var stationFiles = GetAudioFilesForPart("City", divertedStation, language);
                                files.AddRange(stationFiles);
                            }
                        }
                        break;

                    case "EXPECTED_TIME":
                        LogDebug($"ProcessPlaceholderWithTrainData: Processing EXPECTED_TIME placeholder for train {trainNo}");
                        // Get expected time from online train data
                        var onlineTrain = trainService.GetOnlineTrainByNumber(trainNo);
                        if (onlineTrain != null)
                        {
                            string timeValue = onlineTrain["Exp_AT"]?.ToString();
                            LogDebug($"ProcessPlaceholderWithTrainData: Retrieved time value: '{timeValue}'");
                            if (!string.IsNullOrEmpty(timeValue) && timeValue != "--")
                            {
                                var timeFiles = GetExpectedTimeFiles(trainNo, "A", language);
                                LogDebug($"ProcessPlaceholderWithTrainData: Got {timeFiles.Count} time files from GetExpectedTimeFiles");
                                files.AddRange(timeFiles);
                            }
                            else
                            {
                                LogDebug($"ProcessPlaceholderWithTrainData: Time value is empty or '--': '{timeValue}'");
                            }
                        }
                        else
                        {
                            LogDebug($"ProcessPlaceholderWithTrainData: Online train not found for train number: {trainNo}");
                        }
                        break;

                    case "DELAY_TIME":
                        // Get delay time from online train data
                        var onlineTrainData = trainService.GetOnlineTrainByNumber(trainNo);
                        if (onlineTrainData != null)
                        {
                            string delayValue = onlineTrainData["Late"]?.ToString();
                            if (!string.IsNullOrEmpty(delayValue) && delayValue != "--" && delayValue != "00:00")
                            {
                                var delayFiles = GetDelayTimeFiles(trainNo, language);
                                files.AddRange(delayFiles);
                            }
                        }
                        break;

                    case "VIA_STATION_1":
                    case "VIA_STATION_2":
                    case "VIA_STATION_3":
                    case "VIA_STATION_4":
                        // Get via stations from database
                        var trainData3 = trainService.GetTrainByNumber(trainNo);
                        if (trainData3 != null)
                        {
                            int viaIndex = int.Parse(placeholder.Substring(placeholder.Length - 1)) - 1;
                            string viaStationCode = trainData3[$"Via{viaIndex + 1}"]?.ToString();
                            if (!string.IsNullOrEmpty(viaStationCode))
                            {
                                var viaStationFiles = GetAudioFilesForPart("City", viaStationCode, language);
                                files.AddRange(viaStationFiles);
                            }
                        }
                        break;

                    default:
                        LogDebug($"Unknown placeholder: {placeholder}");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error processing placeholder {placeholder}: {ex.Message}");
            }

            return files;
        }

        private async Task LogCurrentLanguagePreferences()
        {
            try
            {
                if (currentStation != null)
                {
                    var announcementConfig = await stationLanguageService.GetStationAnnouncementConfigAsync(currentStation.StationName);
                    if (announcementConfig != null)
                    {
                        LogDebug($"Current station language preferences:");
                        LogDebug($"  First Language: {announcementConfig.FirstLanguageCode} (Enabled: {announcementConfig.FirstLanguageEnabled})");
                        LogDebug($"  Second Language: {announcementConfig.SecondLanguageCode} (Enabled: {announcementConfig.SecondLanguageEnabled})");
                    }
                    else
                    {
                        LogDebug("No announcement configuration found for current station");
                    }
                }
                else
                {
                    LogDebug("No current station configured");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error logging language preferences: {ex.Message}");
            }
        }

        private async Task<List<Models.Language>> GetActiveLanguagesAsync()
        {
            try
            {
                // Get current station's announcement configuration
                if (currentStation != null)
                {
                    var announcementConfig = await stationLanguageService.GetStationAnnouncementConfigAsync(currentStation.StationName);
                    if (announcementConfig != null)
                    {
                        var orderedLanguages = new List<Models.Language>();

                        // Add first language if enabled
                        if (announcementConfig.FirstLanguageEnabled && !string.IsNullOrEmpty(announcementConfig.FirstLanguageCode))
                        {
                            var firstLanguage = await languageService.GetLanguageByCodeAsync(announcementConfig.FirstLanguageCode);
                            if (firstLanguage != null)
                            {
                                orderedLanguages.Add(firstLanguage);
                                LogDebug($"Added first language: {firstLanguage.Name} ({firstLanguage.Code})");
                            }
                        }

                        // Add second language if enabled
                        if (announcementConfig.SecondLanguageEnabled && !string.IsNullOrEmpty(announcementConfig.SecondLanguageCode))
                        {
                            var secondLanguage = await languageService.GetLanguageByCodeAsync(announcementConfig.SecondLanguageCode);
                            if (secondLanguage != null)
                            {
                                orderedLanguages.Add(secondLanguage);
                                LogDebug($"Added second language: {secondLanguage.Name} ({secondLanguage.Code})");
                            }
                        }

                        if (orderedLanguages.Count > 0)
                        {
                            LogDebug($"Retrieved {orderedLanguages.Count} languages from station announcement config: {string.Join(", ", orderedLanguages.Select(l => $"{l.Name}({l.Code})"))}");
                            return orderedLanguages;
                        }
                    }
                }

                // Fallback to all active languages if no station config found
                var languages = await languageService.GetActiveLanguagesAsync();
                LogDebug($"Fallback: Retrieved {languages.Count} active languages from database");
                return languages;
            }
            catch (Exception ex)
            {
                LogDebug($"Error getting active languages: {ex.Message}");
                // Fallback to default languages
                return new List<Models.Language>
                {
                    new Models.Language { Code = "EN", Name = "ENGLISH", WaveFolderPath = "ENGLISH" },
                    new Models.Language { Code = "HI", Name = "HINDI", WaveFolderPath = "HINDI" }
                };
            }
        }

        public List<string> GetAudioFilesForPart(string partType, string partValue, Models.Language language)
        {
            List<string> files = new List<string>();
            string searchPath;

            // Use the language's Name property for the folder path
            string languageFolder = language.Name;

            // Map partType to correct folder structure
            switch (partType.ToUpper())
            {
                case "KEYWORD":
                    searchPath = Path.Combine(fullAudioPath, languageFolder);
                    break;
                case "TRAIN_NO":
                    searchPath = Path.Combine(fullAudioPath, languageFolder, "TRNO");
                    break;
                case "TRAIN_NAME":
                    searchPath = Path.Combine(fullAudioPath, languageFolder, "TRNAME");
                    break;
                case "PLATFORM":
                    searchPath = Path.Combine(fullAudioPath, languageFolder, "PF");
                    break;
                case "STATUS":
                    searchPath = Path.Combine(fullAudioPath, languageFolder, "STD");
                    break;
                case "SLOGAN":
                    searchPath = Path.Combine(fullAudioPath, languageFolder, "SLOGAN");
                    break;
                case "CITY":
                    searchPath = Path.Combine(fullAudioPath, languageFolder, "CITY");
                    break;
                default:
                    searchPath = Path.Combine(fullAudioPath, languageFolder);
                    break;
            }

            LogDebug($"Searching for audio files in: {searchPath}");

            if (!Directory.Exists(searchPath))
            {
                LogDebug($"Directory not found: {searchPath}", true);
                return files;
            }

            // Special handling for different part types
            if (partType.ToUpper() == "KEYWORD")
            {
                // For keywords like EFROM, ETO, etc.
                string filePath = Path.Combine(searchPath, $"{partValue}.wav");
                if (File.Exists(filePath))
                {
                    files.Add(filePath);
                    LogDebug($"Found keyword file: {filePath}");
                }
            }
            else if (partType.ToUpper() == "TRAIN_NO")
            {
                // For train numbers, handle both single digits and full numbers
                string digitPath = Path.Combine(searchPath, $"{partValue}.wav");
                if (File.Exists(digitPath))
                {
                    files.Add(digitPath);
                    LogDebug($"Found train number file: {digitPath}");
                }
            }
            else if (partType.ToUpper() == "TRAIN_NAME")
            {
                // For train names, try to find the train name file
                string trainNamePath = Path.Combine(searchPath, $"{partValue}.wav");
                if (File.Exists(trainNamePath))
                {
                    files.Add(trainNamePath);
                    LogDebug($"Found train name file: {trainNamePath}");
                }
                else
                {
                    // If train name file doesn't exist, try to get train type from database
                    try
                    {
                        var trainData = trainService.GetTrainByNumber(partValue);
                        if (trainData != null)
                        {
                            string trainType = trainData["Train_Type"]?.ToString();
                            if (!string.IsNullOrEmpty(trainType))
                            {
                                // Try to find train type audio file
                                string trainTypePath = Path.Combine(fullAudioPath, "TRAIN TYPE", $"{trainType}.wav");
                                if (File.Exists(trainTypePath))
                                {
                                    files.Add(trainTypePath);
                                    LogDebug($"Found train type file: {trainTypePath}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogDebug($"Error getting train type: {ex.Message}");
                    }
                }
            }
            else if (partType.ToUpper() == "PLATFORM")
            {
                // For platform numbers
                string platformPath = Path.Combine(searchPath, $"{partValue}.wav");
                if (File.Exists(platformPath))
                {
                    files.Add(platformPath);
                    LogDebug($"Found platform file: {platformPath}");
                }
            }
            else if (partType.ToUpper() == "STATUS")
            {
                // For status messages (STD1-STD50)
                string statusPath = Path.Combine(searchPath, $"STD{partValue}.wav");
                if (File.Exists(statusPath))
                {
                    files.Add(statusPath);
                    LogDebug($"Found status file: {statusPath}");
                }
            }
            else
            {
                // For other types, search for exact match
                string filePath = Path.Combine(searchPath, $"{partValue}.wav");
                if (File.Exists(filePath))
                {
                    files.Add(filePath);
                    LogDebug($"Found file: {filePath}");
                }
            }

            return files;
        }

        // Overload for backward compatibility
        public List<string> GetAudioFilesForPart(string partType, string partValue, string languageCode)
        {
            var language = new Models.Language { Code = languageCode, Name = languageCode, WaveFolderPath = languageCode };
            return GetAudioFilesForPart(partType, partValue, language);
        }

        private List<string> GetExpectedTimeFiles(string trainNo, string trainAD, Models.Language language)
        {
            var timeFiles = new List<string>();

            try
            {
                LogDebug($"GetExpectedTimeFiles: Processing train {trainNo}, AD={trainAD}, Language={language.Name}");

                // Get expected time from database
                var onlineTrain = trainService.GetOnlineTrainByNumber(trainNo);
                if (onlineTrain != null)
                {
                    string timeValue = trainAD == "A" ?
                        onlineTrain["Exp_AT"].ToString() :
                        onlineTrain["Exp_DT"].ToString();

                    LogDebug($"GetExpectedTimeFiles: Retrieved time value: '{timeValue}'");

                    if (!string.IsNullOrEmpty(timeValue) && timeValue != "--")
                    {
                        // Support both ':' and '.' separators for time values
                        string[] timeParts;
                        if (timeValue.Contains(':'))
                        {
                            timeParts = timeValue.Split(':');
                        }
                        else if (timeValue.Contains('.'))
                        {
                            timeParts = timeValue.Split('.');
                        }
                        else
                        {
                            LogDebug($"GetExpectedTimeFiles: Invalid time format: {timeValue} (no separator found)");
                            return timeFiles;
                        }

                        if (timeParts.Length == 2)
                        {
                            if (!int.TryParse(timeParts[0], out int hours) || !int.TryParse(timeParts[1], out int minutes))
                            {
                                LogDebug($"GetExpectedTimeFiles: Failed to parse time parts: {timeParts[0]}, {timeParts[1]}");
                                return timeFiles;
                            }

                            LogDebug($"GetExpectedTimeFiles: Parsed time: {hours}:{minutes} from '{timeValue}'");

                            // Add hours
                            if (minutes == 0)
                            {
                                // Use HOUR_Only folder
                                var hourFile = Path.Combine(fullAudioPath, language.Name, "HOUR_Only", $"{hours}.wav");
                                if (File.Exists(hourFile))
                                {
                                    timeFiles.Add(hourFile);
                                }
                            }
                            else
                            {
                                // Add hours
                                var hourFile = Path.Combine(fullAudioPath, language.Name, "HOUR", $"{hours}.wav");
                                if (File.Exists(hourFile))
                                {
                                    timeFiles.Add(hourFile);
                                }

                                // Add minutes
                                var minuteFile = Path.Combine(fullAudioPath, language.Name, "MIN", $"{minutes}.wav");
                                if (File.Exists(minuteFile))
                                {
                                    timeFiles.Add(minuteFile);
                                }
                            }
                        }
                        else
                        {
                            LogDebug($"GetExpectedTimeFiles: Invalid time format, expected 2 parts but got {timeParts.Length}");
                        }
                    }
                    else
                    {
                        LogDebug($"GetExpectedTimeFiles: Time value is empty or '--': '{timeValue}'");
                    }
                }
                else
                {
                    LogDebug($"GetExpectedTimeFiles: Online train not found for train number: {trainNo}");
                }

                LogDebug($"GetExpectedTimeFiles: Returning {timeFiles.Count} time files");
            }
            catch (Exception ex)
            {
                LogDebug($"Error getting expected time files: {ex.Message}");
                LogDebug($"Stack trace: {ex.StackTrace}");
            }

            return timeFiles;
        }

        private List<string> GetDelayTimeFiles(string trainNo, Models.Language language)
        {
            var delayFiles = new List<string>();

            try
            {
                LogDebug($"GetDelayTimeFiles: Processing train {trainNo}, Language={language.Name} ({language.Code})");

                // Get delay time from database
                var onlineTrain = trainService.GetOnlineTrainByNumber(trainNo);
                if (onlineTrain != null)
                {
                    string delayValue = onlineTrain["Late"].ToString();
                    LogDebug($"GetDelayTimeFiles: Retrieved delay value: '{delayValue}'");
                    if (!string.IsNullOrEmpty(delayValue) && delayValue != "--" && delayValue != "00:00")
                    {
                        // Support both ':' and '.' separators for delay values
                        string[] delayParts;
                        if (delayValue.Contains(':'))
                        {
                            delayParts = delayValue.Split(':');
                        }
                        else if (delayValue.Contains('.'))
                        {
                            delayParts = delayValue.Split('.');
                        }
                        else
                        {
                            LogDebug($"GetDelayTimeFiles: Invalid delay format: {delayValue} (no separator found)");
                            return delayFiles;
                        }

                        if (delayParts.Length == 2)
                        {
                            if (!int.TryParse(delayParts[0], out int hours) || !int.TryParse(delayParts[1], out int minutes))
                            {
                                LogDebug($"GetDelayTimeFiles: Failed to parse delay parts: {delayParts[0]}, {delayParts[1]}");
                                return delayFiles;
                            }

                            LogDebug($"GetDelayTimeFiles: Parsed delay: {hours}:{minutes} from '{delayValue}'");

                            // Add delay hours
                            if (hours > 0)
                            {
                                var delayHourFile = Path.Combine(fullAudioPath, language.Name, "DELAY", "HOUR", $"{hours}.wav");
                                if (File.Exists(delayHourFile))
                                {
                                    delayFiles.Add(delayHourFile);
                                }
                            }

                            // Add delay minutes
                            if (minutes > 0)
                            {
                                var delayMinuteFile = Path.Combine(fullAudioPath, language.Name, "DELAY", "MIN", $"{minutes}.wav");
                                if (File.Exists(delayMinuteFile))
                                {
                                    delayFiles.Add(delayMinuteFile);
                                }
                            }
                        }
                        else
                        {
                            LogDebug($"GetDelayTimeFiles: Invalid delay format, expected 2 parts but got {delayParts.Length}");
                        }
                    }
                    else
                    {
                        LogDebug($"GetDelayTimeFiles: Delay value is empty or '--' or '00:00': '{delayValue}'");
                    }
                }
                else
                {
                    LogDebug($"GetDelayTimeFiles: Online train not found for train number: {trainNo}");
                }

                LogDebug($"GetDelayTimeFiles: Returning {delayFiles.Count} delay files");
            }
            catch (Exception ex)
            {
                LogDebug($"Error getting delay time files: {ex.Message}");
                LogDebug($"Stack trace: {ex.StackTrace}");
            }

            return delayFiles;
        }

        private void LogDebug(string message, bool showMessageBox = false)
        {
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            string logMessage = $"[{timestamp}] {message}";

            // Log to console
            Console.WriteLine(logMessage);

            // Log to file
            string logPath = Path.Combine(Application.StartupPath, "announcement_debug.log");
            try
            {
                File.AppendAllText(logPath, logMessage + Environment.NewLine);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error writing to log file: {ex.Message}");
            }
        }

        private void CheckDatabaseContents()
        {
            try
            {
                LogDebug("=== Database Contents Check ===");

                // Check Train_Data table
                var allTrains = trainService.GetAllTrains();
                LogDebug($"Train_Data table has {allTrains?.Rows.Count ?? 0} rows");

                if (allTrains != null && allTrains.Rows.Count > 0)
                {
                    LogDebug("Sample Train_Data rows:");
                    for (int i = 0; i < Math.Min(3, allTrains.Rows.Count); i++)
                    {
                        var row = allTrains.Rows[i];
                        LogDebug($"  Row {i}: Train_No={row["Train_No"]}, Name={row["Train_NameEng"]}, AD={row["Train_AD"]}, AT={row["Sch_AT"]}, DT={row["Sch_DT"]}");
                    }
                }

                // Check Online_Trains table
                var onlineTrains = trainService.GetOnlineTrains();
                LogDebug($"Online_Trains table has {onlineTrains?.Rows.Count ?? 0} rows");

                if (onlineTrains != null && onlineTrains.Rows.Count > 0)
                {
                    LogDebug("Sample Online_Trains rows:");
                    for (int i = 0; i < Math.Min(3, onlineTrains.Rows.Count); i++)
                    {
                        var row = onlineTrains.Rows[i];
                        LogDebug($"  Row {i}: Train_No={row["Train_No"]}, Name={row["Train_NameEng"]}, Status={row["Train_Status"]}");
                    }
                }

                LogDebug("=== End Database Contents Check ===");
            }
            catch (Exception ex)
            {
                LogDebug($"Error checking database contents: {ex.Message}");
            }
        }

        private void TrainGrid_CellMouseClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right && e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                string columnName = trainGrid.Columns[e.ColumnIndex].Name;

                // Show context menu for Late, EAT, and EDT columns
                if (columnName == "Late" || columnName == "EAT" || columnName == "EDT")
                {
                    trainGrid.CurrentCell = trainGrid.Rows[e.RowIndex].Cells[e.ColumnIndex];

                    var contextMenu = new ContextMenuStrip();
                    var updateItem = new ToolStripMenuItem("Update " + columnName + " in Database");
                    updateItem.Click += (s, args) => UpdateExcludedColumn(e.RowIndex, columnName);
                    contextMenu.Items.Add(updateItem);

                    contextMenu.Show(trainGrid, trainGrid.PointToClient(Cursor.Position));
                }
            }
        }

        private void UpdateExcludedColumn(int rowIndex, string columnName)
        {
            try
            {
                var row = trainGrid.Rows[rowIndex];
                string trainNumber = row.Cells["TrainNumber"].Value?.ToString();
                string newValue = row.Cells[columnName].Value?.ToString() ?? "";

                if (string.IsNullOrEmpty(trainNumber))
                {
                    toast.ShowError("Train number not found");
                    return;
                }

                // Get current values from database
                var currentOnlineTrain = trainService.GetOnlineTrainByNumber(trainNumber);
                if (currentOnlineTrain == null)
                {
                    toast.ShowError("Train not found in online list");
                    return;
                }

                string trainName = currentOnlineTrain["Train_NameEng"]?.ToString() ?? "";
                string status = currentOnlineTrain["Train_Status"]?.ToString() ?? "";
                string late = currentOnlineTrain["Late"]?.ToString() ?? "00:00";
                string eat = currentOnlineTrain["Exp_AT"]?.ToString() ?? "";
                string edt = currentOnlineTrain["Exp_DT"]?.ToString() ?? "";
                string pf = currentOnlineTrain["Sch_PF"]?.ToString() ?? "";
                string an = currentOnlineTrain["AN"]?.ToString() ?? "False";

                // Update the specific column value
                switch (columnName)
                {
                    case "Late":
                        late = newValue;
                        break;
                    case "EAT":
                        eat = newValue;
                        break;
                    case "EDT":
                        edt = newValue;
                        break;
                }

                // Update the database
                trainService.UpdateOnlineTrain(
                    trainNumber,
                    trainName,
                    status,
                    late,
                    eat,
                    edt,
                    pf,
                    an
                );

                UpdateStatus($"Manually updated {columnName} for train {trainNumber}");
                LogDebug($"Manually updated {columnName} for train {trainNumber} to value: {newValue}");
            }
            catch (Exception ex)
            {
                LogDebug($"Error updating excluded column {columnName}: {ex.Message}");
                toast.ShowError($"Error updating {columnName}: {ex.Message}");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Stop any ongoing playback
                if (audioPlayer != null)
                {
                    audioPlayer.StopPlayback();
                    audioPlayer.Dispose();
                }

                // Dispose of refresh timer
                if (refreshTimer != null)
                {
                    refreshTimer.Stop();
                    refreshTimer.Dispose();
                }

                // Dispose of auto load/delete timers
                if (autoLoadTimer != null)
                {
                    autoLoadTimer.Stop();
                    autoLoadTimer.Dispose();
                }

                if (autoDeleteTimer != null)
                {
                    autoDeleteTimer.Stop();
                    autoDeleteTimer.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        private async void PlayOnlySlogans()
        {
            try
            {
                var audioQueue = new List<string>();
                var activeLanguages = await GetActiveLanguagesAsync();
                var advertisements = advertisingService.GetAllAdvertisements();
                var slogans = new List<string>();

                foreach (var item in slogansListBox.CheckedItems)
                {
                    string sloganName = item.ToString();
                    slogans.Add(sloganName);

                    // Find the row for this slogan
                    DataRow[] rows = advertisements.Select($"Adver_Name = '{sloganName.Replace("'", "''")}' AND Ann_Type = 'Slogans'");
                    if (rows.Length > 0)
                    {
                        var row = rows[0];
                        string annType = row["Ann_Type"].ToString();
                        string adverName = row["Adver_Name"].ToString();
                        // Get the AdvertisingId using the service's private method via reflection (if needed)
                        // But we can use the repository method via the service
                        var repo = typeof(IPIS.Services.AdvertisingService)
                            .GetField("_repository", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                            .GetValue(advertisingService);
                        var getIdMethod = repo.GetType().GetMethod("GetAdvertisingId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                        int advertisingId = (int)getIdMethod.Invoke(repo, new object[] { annType, adverName });
                        if (advertisingId > 0)
                        {
                            // Get language wave files for this slogan
                            var getWavesMethod = repo.GetType().GetMethod("GetAdvertisementLanguageWaves", new[] { typeof(int) });
                            var languageWaves = (Dictionary<string, string>)getWavesMethod.Invoke(repo, new object[] { advertisingId });
                            foreach (var language in activeLanguages)
                            {
                                string waveFile = null;
                                if (languageWaves.TryGetValue(language.Name, out waveFile) && !string.IsNullOrEmpty(waveFile))
                                {
                                    audioQueue.Add(waveFile);
                                }
                                else if (languageWaves.TryGetValue(language.Code, out waveFile) && !string.IsNullOrEmpty(waveFile))
                                {
                                    audioQueue.Add(waveFile);
                                }
                            }
                        }
                    }
                }

                int repeatCount = (int)repeatCountUpDown.Value;
                audioPlayer.SetAudioQueue(audioQueue, repeatCount);

                if (audioQueue.Count > 0)
                {
                    // Log slogan playback
                    string announcementMessage = $"Slogans: {string.Join(", ", slogans)}";
                    string detailsMessage = $"Started slogan playback with {audioQueue.Count} files, repeat count: {repeatCount}, " +
                                          $"Languages: {string.Join(", ", activeLanguages.Select(l => l.Code))}";

                    Logger.LogAnnouncementMade(announcementMessage, detailsMessage);

                    audioPlayer.StartPlayback();
                    UpdateStatus($"Playing {audioQueue.Count} slogan file(s)");
                }
                else
                {
                    UpdateStatus("No slogan audio files found to play");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error in PlayOnlySlogans: {ex.Message}", true);
                UpdateStatus($"Error: {ex.Message}");

                // Log announcement playback error
                Logger.LogError(LogCategory.Announcement, "Slogan playback failed",
                               $"Error: {ex.Message}", "AnnouncementBoardForm.PlayOnlySlogans", ex);
            }
        }
    }
}