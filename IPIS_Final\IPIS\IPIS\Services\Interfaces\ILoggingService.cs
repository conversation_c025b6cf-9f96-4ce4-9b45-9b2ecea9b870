using System;
using System.Collections.Generic;
using System.Data;
using IPIS.Models;

namespace IPIS.Services.Interfaces
{
    public interface ILoggingService
    {
        // System Logging
        void LogDebug(LogCategory category, string message, string details = null, string source = null);
        void LogInfo(LogCategory category, string message, string details = null, string source = null);
        void LogWarning(LogCategory category, string message, string details = null, string source = null);
        void LogError(LogCategory category, string message, string details = null, string source = null, Exception exception = null);
        void LogCritical(LogCategory category, string message, string details = null, string source = null, Exception exception = null);

        // User Activity Logging
        void LogUserActivity(string action, LogCategory category, string entityType = null, string entityId = null,
                           string oldValues = null, string newValues = null);

        // Retrieval Methods
        DataTable GetSystemLogs(DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null,
                               LogCategory? category = null, string searchText = null, int limit = 1000, int offset = 0);
        DataTable GetUserActivityLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                                     string username = null, string action = null, int limit = 1000, int offset = 0);
        DataTable GetErrorLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                              string searchText = null, int limit = 1000, int offset = 0);
        DataTable GetInfoLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                             string searchText = null, int limit = 1000, int offset = 0);

        // Statistics
        int GetLogCount(LogLevel? level = null, LogCategory? category = null, DateTime? startDate = null, DateTime? endDate = null);
        Dictionary<LogLevel, int> GetLogCountByLevel(DateTime? startDate = null, DateTime? endDate = null);
        Dictionary<LogCategory, int> GetLogCountByCategory(DateTime? startDate = null, DateTime? endDate = null);

        // Maintenance
        void CleanupOldLogs(int daysToKeep = 30);
        void ExportLogs(string filePath, DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null);
    }
}
