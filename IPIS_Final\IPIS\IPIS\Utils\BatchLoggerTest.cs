using System;
using System.Collections.Generic;
using IPIS.Models;

namespace IPIS.Utils
{
    /// <summary>
    /// Test class to demonstrate batch logging functionality
    /// </summary>
    public static class BatchLoggerTest
    {
        /// <summary>
        /// Test method to demonstrate system boot logging
        /// </summary>
        public static void TestSystemBootLogging()
        {
            using var bootLogger = BatchLoggerExtensions.CreateSystemBootLogger();

            bootLogger.LogStep("Initializing application configuration");
            System.Threading.Thread.Sleep(100); // Simulate work

            bootLogger.LogStep("Loading configuration files");
            System.Threading.Thread.Sleep(50);

            bootLogger.LogStep("Initializing database connections");
            System.Threading.Thread.Sleep(200);

            bootLogger.LogStep("Loading user preferences");
            System.Threading.Thread.Sleep(75);

            bootLogger.LogStep("System ready");

            // Logger will automatically complete when disposed
        }

        /// <summary>
        /// Test method to demonstrate data loading logging
        /// </summary>
        public static void TestDataLoadingLogging()
        {
            using var dataLogger = BatchLoggerExtensions.CreateTrainDataLoader();

            // Simulate processing 100 train records
            for (int i = 1; i <= 100; i++)
            {
                if (i % 10 == 0) // Simulate some failures
                {
                    dataLogger.IncrementFailure($"Failed to process train T{i:D3}: Network timeout");
                }
                else
                {
                    dataLogger.IncrementSuccess();
                }

                // Simulate processing time
                if (i % 20 == 0)
                {
                    System.Threading.Thread.Sleep(10);
                }
            }

            // Logger will automatically complete when disposed
        }

        /// <summary>
        /// Test method to demonstrate batch operation logging
        /// </summary>
        public static void TestBatchOperationLogging()
        {
            var operationDetails = new List<string>
            {
                "Cleared Train_Data table (150 records)",
                "Cleared Online_Trains table (25 records)",
                "Reset auto-increment counters",
                "Optimized database indexes",
                "Updated statistics"
            };

            Logger.LogBatchOperation(
                LogCategory.Database,
                "Database Cleanup",
                "Successfully cleaned up database tables",
                operationDetails,
                "BatchLoggerTest"
            );
        }

        /// <summary>
        /// Test method to demonstrate verbose logging control
        /// </summary>
        public static void TestVerboseLoggingControl()
        {
            // Test with verbose logging enabled (default)
            Logger.LogTrainAdded("T001", "Express Train");
            Logger.LogTrainAdded("T002", "Local Train");

            // Disable verbose logging
            Logger.SetVerboseLogging(false);

            // These should not create individual log entries
            Logger.LogTrainAdded("T003", "Freight Train");
            Logger.LogTrainAdded("T004", "Passenger Train");

            // Re-enable verbose logging
            Logger.SetVerboseLogging(true);

            // This should create a log entry again
            Logger.LogTrainAdded("T005", "High Speed Train");
        }

        /// <summary>
        /// Test method to demonstrate online train bulk logging
        /// </summary>
        public static void TestOnlineTrainBulkLogging()
        {
            using var onlineTrainLogger = BatchLoggerExtensions.CreateTrainDataLoader();

            // Simulate bulk online train addition
            Logger.SetVerboseLogging(false); // Suppress individual logs

            for (int i = 1; i <= 50; i++)
            {
                try
                {
                    // Simulate adding online train
                    Logger.LogOnlineTrainAdded($"T{i:D3}", $"Express Train {i}", "RUNNING RIGHT TIME", "1");
                    onlineTrainLogger.IncrementSuccess();
                }
                catch (Exception ex)
                {
                    onlineTrainLogger.IncrementFailure($"Failed to add online train T{i:D3}: {ex.Message}");
                }
            }

            Logger.SetVerboseLogging(true); // Re-enable verbose logging

            // Logger will automatically complete when disposed
        }

        /// <summary>
        /// Run all batch logging tests
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("Testing System Boot Logging...");
                TestSystemBootLogging();

                Console.WriteLine("Testing Data Loading Logging...");
                TestDataLoadingLogging();

                Console.WriteLine("Testing Batch Operation Logging...");
                TestBatchOperationLogging();

                Console.WriteLine("Testing Verbose Logging Control...");
                TestVerboseLoggingControl();

                Console.WriteLine("Testing Online Train Bulk Logging...");
                TestOnlineTrainBulkLogging();

                Console.WriteLine("All batch logging tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during batch logging tests: {ex.Message}");
                Logger.LogError(LogCategory.System, "Batch logging test failed", ex.Message, "BatchLoggerTest", ex);
            }
        }
    }
}
