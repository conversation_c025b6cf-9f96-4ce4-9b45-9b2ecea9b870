using System;

namespace IPIS.Utils
{
    /// <summary>
    /// Helper class for managing pagination state and calculations
    /// </summary>
    public class PaginationHelper
    {
        public int CurrentPage { get; private set; }
        public int PageSize { get; private set; }
        public int TotalRecords { get; private set; }
        public int TotalPages => TotalRecords > 0 ? (int)Math.Ceiling((double)TotalRecords / PageSize) : 0;
        public int Offset => (CurrentPage - 1) * PageSize;
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int StartRecord => TotalRecords > 0 ? Offset + 1 : 0;
        public int EndRecord => Math.Min(Offset + PageSize, TotalRecords);

        public PaginationHelper(int pageSize = 50)
        {
            CurrentPage = 1;
            PageSize = pageSize;
            TotalRecords = 0;
        }

        public void SetTotalRecords(int totalRecords)
        {
            TotalRecords = totalRecords;
            // Ensure current page is valid
            if (CurrentPage > TotalPages && TotalPages > 0)
            {
                CurrentPage = TotalPages;
            }
        }

        public void SetPageSize(int pageSize)
        {
            if (pageSize <= 0)
                throw new ArgumentException("Page size must be greater than 0", nameof(pageSize));

            PageSize = pageSize;
            // Recalculate current page to maintain position as much as possible
            int currentOffset = Offset;
            CurrentPage = Math.Max(1, (currentOffset / PageSize) + 1);
            
            // Ensure current page is valid
            if (CurrentPage > TotalPages && TotalPages > 0)
            {
                CurrentPage = TotalPages;
            }
        }

        public bool GoToPage(int page)
        {
            if (page < 1 || (TotalPages > 0 && page > TotalPages))
                return false;

            CurrentPage = page;
            return true;
        }

        public bool GoToFirstPage()
        {
            return GoToPage(1);
        }

        public bool GoToLastPage()
        {
            return TotalPages > 0 && GoToPage(TotalPages);
        }

        public bool GoToPreviousPage()
        {
            return HasPreviousPage && GoToPage(CurrentPage - 1);
        }

        public bool GoToNextPage()
        {
            return HasNextPage && GoToPage(CurrentPage + 1);
        }

        public string GetDisplayText()
        {
            if (TotalRecords == 0)
                return "No records found";

            return $"Showing {StartRecord}-{EndRecord} of {TotalRecords} records (Page {CurrentPage} of {TotalPages})";
        }

        public void Reset()
        {
            CurrentPage = 1;
            TotalRecords = 0;
        }
    }
}
