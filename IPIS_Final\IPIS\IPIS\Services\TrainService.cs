using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Services
{
    public class TrainService
    {
        private readonly ITrainRepository _trainRepository;

        public TrainService(ITrainRepository trainRepository)
        {
            _trainRepository = trainRepository;
        }

        public DataTable GetAllTrains()
        {
            return _trainRepository.GetAllTrains();
        }

        public DataTable GetOnlineTrains()
        {
            return _trainRepository.GetOnlineTrains();
        }

        public DataTable GetTrainsForCurrentDay()
        {
            return _trainRepository.GetTrainsForCurrentDay();
        }

        public DataTable GetTrainsForCurrentTimeWindow(int timeWindowMinutes = 120)
        {
            return _trainRepository.GetTrainsForCurrentTimeWindow(timeWindowMinutes);
        }

        public DataTable GetTrainsForCurrentDayAndTime(int timeWindowMinutes = 120)
        {
            return _trainRepository.GetTrainsForCurrentDayAndTime(timeWindowMinutes);
        }

        public void AddTrain(string trainNo, string trainName, string trainType, string trainAD, string schAT, string schDT, string schPF, string srcStn, string destiStn, string[] viaStations, bool[] operatingDays)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["trainNo"] = trainNo,
                ["trainName"] = trainName,
                ["trainType"] = trainType,
                ["trainAD"] = trainAD,
                ["schAT"] = schAT,
                ["schDT"] = schDT,
                ["schPF"] = schPF,
                ["srcStn"] = srcStn,
                ["destiStn"] = destiStn,
                ["viaStations"] = string.Join(",", viaStations ?? new string[0]),
                ["operatingDays"] = string.Join(",", operatingDays?.Select(d => d.ToString()) ?? new string[0])
            };

            try
            {
                // Log request
                Logger.LogRequest(LogCategory.TrainManagement, "AddTrain", requestParams, "TrainService.AddTrain");

                _trainRepository.AddTrain(trainNo, trainName, trainType, trainAD, schAT, schDT, schPF, srcStn, destiStn, viaStations, operatingDays);

                // Log successful response
                var responseData = new { trainNo = trainNo, trainName = trainName, status = "added" };
                Logger.LogResponse(LogCategory.TrainManagement, "AddTrain", true, responseData, null, "TrainService.AddTrain");
                Logger.LogDatabaseOperation("INSERT", "Train_Data", true, $"Train {trainNo} added successfully");
            }
            catch (Exception ex)
            {
                // Log failed response
                Logger.LogResponse(LogCategory.TrainManagement, "AddTrain", false, null, ex.Message, "TrainService.AddTrain");
                Logger.LogDatabaseOperation("INSERT", "Train_Data", false, $"Failed to add train {trainNo}: {ex.Message}");
                throw;
            }
        }

        public void UpdateTrain(string trainNo, string trainName, string trainType, string trainAD, string schAT, string schDT, string schPF, string srcStn, string destiStn, string[] viaStations, bool[] operatingDays)
        {
            try
            {
                // Get old values for logging
                var oldTrain = GetTrainByNumber(trainNo);
                string oldValues = oldTrain != null ?
                    $"Name: {oldTrain["Train_NameEng"]}, Type: {oldTrain["Train_Type"]}, Source: {oldTrain["Src_Stn"]}, Destination: {oldTrain["Desti_Stn"]}" :
                    "Previous values not available";

                string newValues = $"Name: {trainName}, Type: {trainType}, Source: {srcStn}, Destination: {destiStn}";

                _trainRepository.UpdateTrain(trainNo, trainName, trainType, trainAD, schAT, schDT, schPF, srcStn, destiStn, viaStations, operatingDays);

                // Log successful train update
                Logger.LogTrainUpdated(trainNo, oldValues, newValues);
                Logger.LogDatabaseOperation("UPDATE", "Train_Data", true, $"Train {trainNo} updated successfully");
            }
            catch (Exception ex)
            {
                // Log failed train update
                Logger.LogError(LogCategory.TrainManagement, $"Failed to update train: {trainNo}",
                               $"Error: {ex.Message}", "TrainService.UpdateTrain", ex);
                Logger.LogDatabaseOperation("UPDATE", "Train_Data", false, $"Failed to update train {trainNo}: {ex.Message}");
                throw;
            }
        }

        public void DeleteTrain(string trainNo)
        {
            try
            {
                // Get train details before deletion for logging
                var trainToDelete = GetTrainByNumber(trainNo);
                string trainName = trainToDelete?["Train_NameEng"]?.ToString() ?? "Unknown";

                _trainRepository.DeleteTrain(trainNo);

                // Log successful train deletion
                Logger.LogTrainDeleted(trainNo, trainName);
                Logger.LogDatabaseOperation("DELETE", "Train_Data", true, $"Train {trainNo} deleted successfully");
            }
            catch (Exception ex)
            {
                // Log failed train deletion
                Logger.LogError(LogCategory.TrainManagement, $"Failed to delete train: {trainNo}",
                               $"Error: {ex.Message}", "TrainService.DeleteTrain", ex);
                Logger.LogDatabaseOperation("DELETE", "Train_Data", false, $"Failed to delete train {trainNo}: {ex.Message}");
                throw;
            }
        }

        public void UpdateOnlineTrain(string trainNo, string trainName, string status, string late, string expAT, string expDT, string platform, string announcement)
        {
            _trainRepository.UpdateOnlineTrain(trainNo, trainName, status, late, expAT, expDT, platform, announcement);
        }

        public void UpdateOnlineTrain(string trainNo, string trainName, string status, string late, string expAT, string expDT, string platform, string announcement, string changedPF = "", string divertedFrom = "")
        {
            _trainRepository.UpdateOnlineTrain(trainNo, trainName, status, late, expAT, expDT, platform, announcement, changedPF, divertedFrom);
        }

        public void DeleteOnlineTrain(string trainNo)
        {
            try
            {
                // Get train details before deletion for logging
                var onlineTrain = GetOnlineTrainByNumber(trainNo);
                string trainName = onlineTrain?["Train_Name"]?.ToString() ?? "Unknown";

                _trainRepository.DeleteOnlineTrain(trainNo);

                // Log successful online train removal
                Logger.LogInfo(LogCategory.TrainManagement, $"Online train removed: {trainNo}",
                              $"Train Name: {trainName}", "TrainService.DeleteOnlineTrain");
                Logger.LogUserActivity("Remove Online Train", LogCategory.TrainManagement, "OnlineTrain", trainNo,
                                      $"Train_No: {trainNo}, Train_Name: {trainName}", null);
                Logger.LogDatabaseOperation("DELETE", "Online_Trains", true, $"Online train {trainNo} removed successfully");
            }
            catch (Exception ex)
            {
                // Log failed online train removal
                Logger.LogError(LogCategory.TrainManagement, $"Failed to remove online train: {trainNo}",
                               $"Error: {ex.Message}", "TrainService.DeleteOnlineTrain", ex);
                Logger.LogDatabaseOperation("DELETE", "Online_Trains", false, $"Failed to remove online train {trainNo}: {ex.Message}");
                throw;
            }
        }

        public DataRow GetTrainByNumber(string trainNo)
        {
            var trains = GetAllTrains();
            var rows = trains.Select($"Train_No = '{trainNo}'");
            return rows.Length > 0 ? rows[0] : null;
        }

        public DataRow GetOnlineTrainByNumber(string trainNo)
        {
            var trains = GetOnlineTrains();
            var rows = trains.Select($"Train_No = '{trainNo}'");
            return rows.Length > 0 ? rows[0] : null;
        }

        public void AddOnlineTrain(string trainNo, string trainName, string trainAD, string status, string late, string eat, string edt, string pf, string an)
        {
            try
            {
                _trainRepository.AddOnlineTrain(trainNo, trainName, trainAD, status, late, eat, edt, pf, an);

                // Log successful online train addition
                Logger.LogInfo(LogCategory.TrainManagement, $"Online train added: {trainNo}",
                              $"Train Name: {trainName}, Status: {status}, Platform: {pf}", "TrainService.AddOnlineTrain");
                Logger.LogUserActivity("Add Online Train", LogCategory.TrainManagement, "OnlineTrain", trainNo, null,
                                      $"Train_No: {trainNo}, Train_Name: {trainName}, Status: {status}, Platform: {pf}");
                Logger.LogDatabaseOperation("INSERT", "Online_Trains", true, $"Online train {trainNo} added successfully");
            }
            catch (Exception ex)
            {
                // Log failed online train addition
                Logger.LogError(LogCategory.TrainManagement, $"Failed to add online train: {trainNo}",
                               $"Error: {ex.Message}", "TrainService.AddOnlineTrain", ex);
                Logger.LogDatabaseOperation("INSERT", "Online_Trains", false, $"Failed to add online train {trainNo}: {ex.Message}");
                throw;
            }
        }

        public void AddOnlineTrain(string trainNo, string trainName, string trainAD, string status, string late, string eat, string edt, string pf, string an, string changedPF = "", string divertedFrom = "")
        {
            try
            {
                _trainRepository.AddOnlineTrain(trainNo, trainName, trainAD, status, late, eat, edt, pf, an, changedPF, divertedFrom);

                // Log successful online train addition with additional details
                Logger.LogInfo(LogCategory.TrainManagement, $"Online train added: {trainNo}",
                              $"Train Name: {trainName}, Status: {status}, Platform: {pf}, Changed PF: {changedPF}, Diverted From: {divertedFrom}",
                              "TrainService.AddOnlineTrain");
                Logger.LogUserActivity("Add Online Train", LogCategory.TrainManagement, "OnlineTrain", trainNo, null,
                                      $"Train_No: {trainNo}, Train_Name: {trainName}, Status: {status}, Platform: {pf}, Changed_PF: {changedPF}, Diverted_From: {divertedFrom}");
                Logger.LogDatabaseOperation("INSERT", "Online_Trains", true, $"Online train {trainNo} added successfully with extended details");
            }
            catch (Exception ex)
            {
                // Log failed online train addition
                Logger.LogError(LogCategory.TrainManagement, $"Failed to add online train: {trainNo}",
                               $"Error: {ex.Message}", "TrainService.AddOnlineTrain", ex);
                Logger.LogDatabaseOperation("INSERT", "Online_Trains", false, $"Failed to add online train {trainNo}: {ex.Message}");
                throw;
            }
        }

        public void ClearAllOnlineTrains()
        {
            _trainRepository.ClearAllOnlineTrains();
        }

        public void ClearAllTrains()
        {
            try
            {
                var trainCount = GetAllTrains().Rows.Count;
                _trainRepository.ClearAllTrains();

                // Use batch logging for bulk operations
                Logger.LogBatchOperation(LogCategory.TrainManagement, "Clear All Trains",
                    $"Successfully cleared {trainCount} train records",
                    new List<string> { $"Cleared {trainCount} records from Train_Data table" },
                    "TrainService.ClearAllTrains");
            }
            catch (Exception ex)
            {
                // Log failed train data clearing
                Logger.LogError(LogCategory.TrainManagement, "Failed to clear all train data",
                               $"Error: {ex.Message}", "TrainService.ClearAllTrains", ex);
                Logger.LogDatabaseOperation("DELETE", "Train_Data", false, $"Failed to clear all train data: {ex.Message}");
                throw;
            }
        }
    }
}