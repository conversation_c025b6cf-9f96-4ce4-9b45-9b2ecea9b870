using System;
using System.Windows.Forms;
using IPIS.Forms.User;
using IPIS.Forms.Announcement;
using IPIS.Repositories;
using IPIS.Services;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                ApplicationConfiguration.Initialize();

                // Initialize the database (create tables if they don't exist)
                Database.EnsureDatabaseInitialized();

                // Initialize LanguageManager
                var languageService = new LanguageService(new SQLiteLanguageRepository());
                LanguageManager.Initialize(languageService);

                // Log system startup
                Logger.LogSystemStartup();

                // Show login form
                using (var loginForm = new LoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // If login is successful, show main form
                        Application.Run(new MainForm());
                    }
                }

                // Log system shutdown
                Logger.LogSystemShutdown();
            }
            catch (Exception ex)
            {
                // Log critical error
                Logger.LogCritical(LogCategory.System, "Application startup failed",
                                 ex.Message, "Program.Main", ex);

                MessageBox.Show($"A critical error occurred during application startup: {ex.Message}",
                              "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}